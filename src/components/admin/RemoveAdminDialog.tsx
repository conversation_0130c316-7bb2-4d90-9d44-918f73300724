'use client';

import { useState } from 'react';
import { Trash, Alert<PERSON>riangle, Loader2 } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { removeAdmin } from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';
import { AdminUser } from '@/actions/admin';

interface RemoveAdminDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  admin: AdminUser | null;
  onAdminRemoved: () => void;
}

export function RemoveAdminDialog({
  open,
  onOpenChange,
  admin,
  onAdminRemoved,
}: RemoveAdminDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  if (!admin) return null;

  const getDisplayName = () => {
    return (
      `${admin.first_name || ''} ${admin.last_name || ''}`.trim() || admin.email
    );
  };

  const handleRemove = async () => {
    setIsSubmitting(true);

    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      const result = await removeAdmin(admin.uid, accessToken, idToken);

      if (result.ok) {
        toast.success('Admin removed successfully');
        onAdminRemoved();
        onOpenChange(false);
      } else {
        toast.error(result.error || 'Failed to remove admin');
      }
    } catch (error) {
      console.error('Error removing admin:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            Remove Admin
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This action cannot be undone. The admin will lose all access to
              the platform.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <p className="text-sm">
              Are you sure you want to remove{' '}
              <strong>{getDisplayName()}</strong> from admin access?
            </p>
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm text-muted-foreground">
                <strong>Email:</strong> {admin.email}
              </p>
              <p className="text-sm text-muted-foreground">
                <strong>Admin Level:</strong> {admin.admin_level || 'viewer'}
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleRemove}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  Remove Admin
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
