import { getAdminClinics } from '@/actions/admin';
import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Globe,
  ClipboardList,
  ChevronRight,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ClinicType } from '@/lib/types';
import { fetchAuthSession } from 'aws-amplify/auth';

const ClinicSkeleton = () => (
  <div className="flex items-center animate-pulse">
    <div className="flex h-9 w-9 items-center justify-center rounded-full bg-muted"></div>
    <div className="ml-4 space-y-2 flex-1">
      <div className="h-4 bg-muted rounded w-3/4"></div>
      <div className="h-3 bg-muted rounded w-1/2"></div>
    </div>
    <div className="ml-auto text-right">
      <div className="h-4 bg-muted rounded w-16 mb-2"></div>
      <div className="h-3 bg-muted rounded w-12"></div>
    </div>
  </div>
);

const ActivitySkeleton = () => (
  <div className="flex items-center animate-pulse">
    <div className="flex h-9 w-9 items-center justify-center rounded-full bg-muted"></div>
    <div className="ml-4 space-y-2 flex-1">
      <div className="h-4 bg-muted rounded w-3/4"></div>
      <div className="h-3 bg-muted rounded w-full"></div>
    </div>
  </div>
);

const ClinicsList = () => {
  const [clinics, setClinics] = useState<ClinicType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalClinics, setTotalClinics] = useState<number>(0);
  const [activeClinics, setActiveClinics] = useState<number>(0);

  useEffect(() => {
    async function fetchClinics() {
      try {
        setLoading(true);
        setError(null);

        // Get token from Amplify auth session
        const session = await fetchAuthSession({ forceRefresh: true });
        const token = session.tokens?.accessToken?.toString() || '';
        const idToken = session.tokens?.idToken?.toString() || '';

        const result = await getAdminClinics(token, idToken);

        if (result.ok && result.data) {
          const clinicsList = result.data.data || [];
          setClinics(clinicsList);
          setTotalClinics(clinicsList.length);

          const active = clinicsList.filter(
            (clinic: ClinicType) => clinic.is_active,
          ).length;
          setActiveClinics(active);
        } else {
          setError(result.error || 'Failed to fetch clinics');
        }
      } catch (err) {
        console.error('Error in fetching clinics:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchClinics();
  }, []);

  const retryFetch = async () => {
    setLoading(true);
    setError(null);
    setClinics([]);

    try {
      // Get token from Amplify auth session
      const session = await fetchAuthSession({ forceRefresh: true });
      const token = session.tokens?.accessToken?.toString() || '';
      const idToken = session.tokens?.idToken?.toString() || '';

      // Re-fetch clinics after state reset
      const result = await getAdminClinics(token, idToken);

      if (result.ok && result.data) {
        const clinicsList = result.data.data || [];
        setClinics(clinicsList);
        setTotalClinics(clinicsList.length);

        const active = clinicsList.filter(
          (clinic: ClinicType) => clinic.is_active,
        ).length;
        setActiveClinics(active);
      } else {
        setError(result.error || 'Failed to fetch clinics');
      }
    } catch (err) {
      console.error('Error in fetching clinics:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const renderClinicContent = () => {
    if (error) {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={retryFetch}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    if (loading) {
      return (
        <div className="space-y-4">
          {Array(5)
            .fill(0)
            .map((_, i) => (
              <ClinicSkeleton key={i} />
            ))}
        </div>
      );
    }

    if (clinics.length === 0) {
      return (
        <div className="text-center py-8">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">No clinics found</h3>
          <p className="text-muted-foreground">
            No clinics have been registered yet.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {clinics.slice(0, 5).map((clinic) => (
          <div key={clinic._id} className="flex items-center">
            <div
              className={`flex h-9 w-9 items-center justify-center rounded-full ${
                clinic.is_active ? 'bg-primary/20' : 'bg-muted'
              }`}
            >
              <Building2
                className={`h-5 w-5 ${
                  clinic.is_active ? 'text-primary' : 'text-muted-foreground'
                }`}
              />
            </div>
            <div className="ml-4 space-y-1">
              <p className="text-sm font-medium leading-none text-foreground">
                {clinic.clinic_name}
              </p>
              <p className="text-sm text-muted-foreground">
                {clinic.clinic_email}
              </p>
            </div>
            <div className="ml-auto text-right text-sm">
              <p className="font-medium">
                <a
                  href={clinic.clinic_website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-indigo-600 hover:underline flex items-center"
                >
                  <Globe className="h-3 w-3 mr-1" />
                  Website
                </a>
              </p>
              <p
                className={`capitalize text-xs bg-emerald-50 w-fit ml-auto px-2 py-0.5 rounded-3xl ${
                  clinic.is_active
                    ? 'text-emerald-500'
                    : 'text-muted-foreground'
                }`}
              >
                {clinic.is_active ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderActivityContent = () => {
    if (loading) {
      return (
        <div className="space-y-4">
          {Array(3)
            .fill(0)
            .map((_, i) => (
              <ActivitySkeleton key={i} />
            ))}
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center">
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/20">
              <ClipboardList className="h-5 w-5 text-primary" />
            </div>
            <div className="ml-4 space-y-1">
              <p className="text-sm font-medium leading-none text-foreground">
                {i === 1
                  ? 'New clinic registered'
                  : i === 2
                    ? 'User role updated'
                    : 'System maintenance completed'}
              </p>
              <p className="text-sm text-muted-foreground">
                {i === 1
                  ? 'New clinic joined the platform'
                  : i === 2
                    ? 'Dr. Sarah Johnson promoted to admin'
                    : 'System updated to version 2.1.0'}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
      <Card className="md:col-span-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Clinics</CardTitle>
              <CardDescription>
                {loading ? (
                  <span className="flex items-center">
                    <Loader2 className="h-3 w-3 animate-spin mr-2" />
                    Loading clinics...
                  </span>
                ) : (
                  `${totalClinics} total, ${activeClinics} active`
                )}
              </CardDescription>
            </div>
            {loading && (
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            )}
          </div>
        </CardHeader>
        <CardContent>{renderClinicContent()}</CardContent>
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            disabled={loading || clinics.length === 0}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                View all clinics
                <ChevronRight className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card className="md:col-span-3">
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>Latest system activities</CardDescription>
        </CardHeader>
        <CardContent>{renderActivityContent()}</CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                View all activities
                <ChevronRight className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ClinicsList;
