'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import {
  registerAdmin,
  AdminPrivilegeLevel,
  RegisterAdminPayload,
} from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';

interface AddAdminDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAdminAdded: () => void;
}

export function AddAdminDialog({
  open,
  onOpenChange,
  onAdminAdded,
}: AddAdminDialogProps) {
  const [email, setEmail] = useState('');
  const [privilegeLevel, setPrivilegeLevel] = useState<AdminPrivilegeLevel>(
    AdminPrivilegeLevel.VIEWER,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    if (!privilegeLevel) {
      toast.error('Please select a privilege level');
      return;
    }

    setIsSubmitting(true);

    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      const payload: RegisterAdminPayload = {
        email: email.trim(),
        privilege_level: privilegeLevel,
      };

      const result = await registerAdmin(payload, accessToken, idToken);

      if (result.ok) {
        toast.success('Admin added successfully');
        setEmail('');
        setPrivilegeLevel(AdminPrivilegeLevel.VIEWER);
        onAdminAdded();
        onOpenChange(false);
      } else {
        toast.error(result?.error);
      }
    } catch (error) {
      console.error('Error adding admin:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setEmail('');
      setPrivilegeLevel(AdminPrivilegeLevel.VIEWER);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add New Admin
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter email address (e.g., <EMAIL>)"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isSubmitting}
              required
            />
            <p className="text-sm text-muted-foreground">
              Enter the email address of the user you want to make an admin.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="privilege-level">Privilege Level</Label>
            <Select
              value={privilegeLevel}
              onValueChange={(value) =>
                setPrivilegeLevel(value as AdminPrivilegeLevel)
              }
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select privilege level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={AdminPrivilegeLevel.VIEWER}>
                  Viewer - Read-only access
                </SelectItem>
                <SelectItem value={AdminPrivilegeLevel.EDITOR}>
                  Editor - Can modify content
                </SelectItem>
                <SelectItem value={AdminPrivilegeLevel.OWNER}>
                  Owner - Full administrative access
                </SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Choose the level of administrative access for this user.
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Admin
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
