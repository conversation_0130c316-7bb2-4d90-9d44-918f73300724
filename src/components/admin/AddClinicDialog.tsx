'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building2, Loader2 } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';
import { createOrUpdateClinicForAdmin } from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';

const addClinicSchema = z.object({
  clinic_name: z.string().min(2, {
    message: 'Clinic name must be at least 2 characters.',
  }),
  clinic_email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  clinic_website: z
    .string()
    .url({ message: 'Please enter a valid URL.' })
    .optional()
    .or(z.literal('')),
  clinic_phone: z.string().optional(),
  human_transfer_destination_number: z
    .string()
    .regex(/^\+\d{10,15}$/, {
      message:
        'Please enter a valid phone number in E.164 format, e.g., +12025550184 or +919999998888',
    })
    .optional()
    .or(z.literal('')),
});

type AddClinicFormValues = z.infer<typeof addClinicSchema>;

interface AddClinicDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onClinicAdded: () => void;
}

export function AddClinicDialog({
  open,
  onOpenChange,
  onClinicAdded,
}: AddClinicDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  const form = useForm<AddClinicFormValues>({
    resolver: zodResolver(addClinicSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
      human_transfer_destination_number: '',
    },
  });

  const onSubmit = async (values: AddClinicFormValues) => {
    setIsSubmitting(true);

    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      const clinicData = {
        ...values,
        is_active: true,
        clinic_addresses: [],
        diagnostic_services: [], // Ensure diagnostic_services is included
      };

      const result = await createOrUpdateClinicForAdmin(
        clinicData,
        accessToken,
        idToken,
      );

      if (result.ok) {
        toast.success('Clinic created successfully');
        form.reset();
        onClinicAdded();
        onOpenChange(false);
      } else {
        console.error('Failed to create clinic:', result.error);
        toast.error(result.error || 'Failed to create clinic');
      }
    } catch (error) {
      console.error('Error creating clinic:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Add New Clinic
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="clinic_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Clinic Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter clinic name"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clinic_email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Clinic Email *</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter clinic email"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clinic_website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Clinic Website</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://example.com"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clinic_phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Clinic Phone</FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="Enter clinic phone number"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="human_transfer_destination_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Transfer Number</FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="*************"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Building2 className="mr-2 h-4 w-4" />
                    Create Clinic
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
