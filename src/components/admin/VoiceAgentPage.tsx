'use client';

import { useState, useEffect } from 'react';
import { format, fromUnixTime } from 'date-fns';
import {
  AlertCircle,
  Clock,
  Loader2,
  Mic,
  Volume2,
  Check,
  X,
  LanguagesIcon,
  GraduationCap,
  SpellCheck,
  Zap,
  Timer,
  BookText,
} from 'lucide-react';

import { createVoiceAgent, getVoiceAgent } from '@/actions/voice-agent';
import { VoiceAgent } from '@/lib/agent-types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { useAuth } from '@/contexts/AuthContext';
import { CreateVoiceAgentInput } from '@/lib/agent-types';

export default function VoiceAgentPage() {
  const [voiceAgent, setVoiceAgent] = useState<VoiceAgent | null>(null);

  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const { selectedClinic } = useAdminClinic();
  const { getTokens } = useAuth();

  const handleCreateVoiceAgent = async () => {
    try {
      setSubmitting(true);
      setSuccess(null);
      setError(null);

      const { accessToken, idToken } = await getTokens();
      if (!accessToken || !idToken) {
        setError('Either accessToken or idToken is not available');
        setLoading(false);
        return;
      }
      const clinicId = selectedClinic?._id;

      if (!clinicId) {
        setError('Clinic ID is required');
        setSubmitting(false);
        return;
      }

      const data: CreateVoiceAgentInput = {};
      const result = await createVoiceAgent(
        clinicId,
        data,
        accessToken,
        idToken,
      );

      if (result.ok && result.data) {
        setSuccess('Voice agent created successfully');
        setVoiceAgent(result.data);
      } else {
        setError(result.error || 'Failed to create voice agent');
      }
    } catch (error) {
      console.error('Error creating knowledge base:', error);
      setError('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    async function fetchVoiceAgent() {
      try {
        setLoading(true);
        setError(null);

        const { accessToken, idToken } = await getTokens();

        if (!accessToken || !idToken) {
          setError('Failed to retrieve authentication tokens after sign-up.');
          setLoading(false);
          return;
        }

        const clinicId = selectedClinic?._id;

        if (!clinicId) {
          setError('Clinic ID is required');
          setLoading(false);
          return;
        }

        const result = await getVoiceAgent(clinicId, accessToken, idToken);

        if (result.ok && result.data) {
          setVoiceAgent(result.data);
          console.log('Voice agent data:', result.data);
        } else {
          setVoiceAgent(null);
        }
      } catch (err) {
        console.error('Error fetching voice agent:', err);
        setError('An unexpected error occurred');
        setVoiceAgent(null);
      } finally {
        setLoading(false);
      }
    }

    fetchVoiceAgent();
  }, [selectedClinic, getTokens]);

  const formatTimestamp = (timestamp: number) => {
    try {
      const date =
        timestamp > 9999999999
          ? fromUnixTime(timestamp / 1000)
          : fromUnixTime(timestamp);
      return format(date, 'MMM dd, yyyy HH:mm:ss');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Invalid date';
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">
          Voice Agent Configuration
        </h1>
      </div>

      {loading ? (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mr-2 text-primary" />
              <p>Loading voice agent configuration...</p>
            </div>
          </CardContent>
        </Card>
      ) : success ? (
        <Alert variant="default" className="bg-green-50/10 border-green-500/40">
          <Check className="h-4 w-4 text-green-500" />
          <AlertTitle className="text-green-500">Success</AlertTitle>
          <AlertDescription className="text-green-600">
            {success}
          </AlertDescription>
        </Alert>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2 mt-2"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      ) : voiceAgent ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mic className="h-5 w-5 mr-2 text-primary" />
                  Agent Overview
                </CardTitle>
                <CardDescription>
                  Basic information about the voice agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Agent Name
                      </p>
                      <h3 className="text-lg font-medium">
                        {voiceAgent.data.agent_name}
                      </h3>
                    </div>
                    <div>
                      {voiceAgent.data.is_published ? (
                        <Badge variant={'success'}>
                          <Check className="h-3 w-3 mr-1" /> Published
                        </Badge>
                      ) : (
                        <Badge variant="outline">
                          <Clock className="h-3 w-3 mr-1" /> Draft
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Agent ID
                      </p>
                      <p className="text-sm font-mono">
                        {voiceAgent.data.agent_id}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Version
                      </p>
                      <p className="text-sm">
                        v{voiceAgent.data.version} -{' '}
                        {voiceAgent.data.version_title}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Last Modified
                    </p>
                    <p className="text-sm">
                      {formatTimestamp(
                        voiceAgent.data.last_modification_timestamp,
                      )}
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Response Engine
                    </p>
                    <div className="flex items-center mt-1">
                      <Zap className="h-4 w-4 mr-2 text-indigo-500" />
                      <p>
                        {voiceAgent.data.response_engine.type} (v
                        {voiceAgent.data.response_engine.version})
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      LLM ID: {voiceAgent.data.response_engine.llm_id}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Post-Call Analysis Model
                    </p>
                    <div className="flex items-center mt-1">
                      <GraduationCap className="h-4 w-4 mr-2 text-indigo-500" />
                      <p>{voiceAgent.data.post_call_analysis_model}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Volume2 className="h-5 w-5 mr-2 text-primary" />
                  Voice Configuration
                </CardTitle>
                <CardDescription>Voice settings and parameters</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Voice ID
                      </p>
                      <div className="flex items-center mt-1">
                        <p className="text-sm font-mono">
                          {voiceAgent.data.voice_id}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center">
                      <p className="text-sm font-medium text-muted-foreground">
                        Language
                      </p>
                      <Badge variant="outline" className="ml-2">
                        <LanguagesIcon className="h-3 w-3 mr-1" />{' '}
                        {voiceAgent.data.language}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="call-settings" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="call-settings">Call Settings</TabsTrigger>
              <TabsTrigger value="pronunciation">Pronunciation</TabsTrigger>
            </TabsList>

            <TabsContent value="call-settings">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-primary" />
                    Call Settings
                  </CardTitle>
                  <CardDescription>
                    Configure call behavior and timeouts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">
                          Maximum Call Duration
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Timer className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatDuration(
                              voiceAgent.data.max_call_duration_ms,
                            )}
                          </div>
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell className="font-medium">
                          Sensitive Data Storage
                        </TableCell>
                        <TableCell>
                          {voiceAgent.data.opt_out_sensitive_data_storage ? (
                            <Badge variant="destructive">
                              <X className="h-3 w-3 mr-1" /> Opted Out
                            </Badge>
                          ) : (
                            <Badge variant="success">
                              <Check className="h-3 w-3 mr-1" /> Enabled
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell className="font-medium">
                          Signed URL
                        </TableCell>
                        <TableCell>
                          {voiceAgent.data.opt_in_signed_url ? (
                            <Badge variant="success">
                              <Check className="h-3 w-3 mr-1" /> Enabled
                            </Badge>
                          ) : (
                            <Badge variant="destructive">
                              <X className="h-3 w-3 mr-1" /> Disabled
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            {voiceAgent.data.pronunciation_dictionary && (
              <TabsContent value="pronunciation">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <SpellCheck className="h-5 w-5 mr-2 text-primary" />
                      Pronunciation Dictionary
                    </CardTitle>
                    <CardDescription>
                      Custom word pronunciations using IPA phonetics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {voiceAgent.data.pronunciation_dictionary.length === 0 ? (
                      <div className="text-center py-8">
                        <BookText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium">
                          No pronunciation entries
                        </h3>
                        <p className="text-muted-foreground">
                          No custom pronunciations have been defined for this
                          agent.
                        </p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Word</TableHead>
                            <TableHead>Alphabet</TableHead>
                            <TableHead>Phoneme</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {voiceAgent.data.pronunciation_dictionary.map(
                            (entry, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">
                                  {entry.word}
                                </TableCell>
                                <TableCell>
                                  {entry.alphabet.toUpperCase()}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {entry.phoneme}
                                </TableCell>
                              </TableRow>
                            ),
                          )}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </>
      ) : (
        <Alert className="bg-amber-500/5 text-amber-800 dark:text-amber-400 border-amber-500/40">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Voice Agent Found</AlertTitle>
          <AlertDescription>
            <div className="space-y-3">
              <p>
                No voice agent configuration was found for this clinic. You can
                create a new voice agent configuration.
              </p>
              <Button
                onClick={handleCreateVoiceAgent}
                disabled={submitting}
                className="w-full sm:w-auto"
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>Create Voice Agent</>
                )}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
