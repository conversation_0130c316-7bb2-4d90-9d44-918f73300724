'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';

import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { useAuth } from '@/contexts/AuthContext';
import { createOrUpdateClinicForAdmin } from '@/actions/admin';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const crmDetailsSchema = z.object({
  name: z.enum(['CLINIKO', 'GENIE'], {
    required_error: 'Please select a CRM',
  }),
  auth_details: z.object({
    api_key: z.string().min(1, 'API Key is required'),
    api_endpoint: z.string().optional(),
  }),
  custom_fields: z.object({
    appointment_type_id: z.string().min(1, 'Appointment Type ID is required'),
  }),
});

type CRMDetailsFormValues = z.infer<typeof crmDetailsSchema>;

export default function CRMDetailsPage() {
  const { selectedClinic, setSelectedClinic } = useAdminClinic();
  const { getTokens } = useAuth();
  const [loading, setLoading] = useState(false);

  const form = useForm<CRMDetailsFormValues>({
    resolver: zodResolver(crmDetailsSchema),
    defaultValues: {
      name: 'CLINIKO',
      auth_details: {
        api_key: '',
        api_endpoint: '',
      },
      custom_fields: {
        appointment_type_id: '',
      },
    },
  });

  useEffect(() => {
    if (selectedClinic?.crm_details) {
      interface CRMDetails {
        name?: string;
        auth_details?: {
          api_key?: string;
          api_endpoint?: string;
        };
        custom_fields?: {
          appointment_type_id?: string;
        };
      }

      const crmDetails = selectedClinic.crm_details as CRMDetails;
      const formValues = {
        name: (crmDetails.name as 'CLINIKO' | 'GENIE') || 'CLINIKO',
        auth_details: {
          api_key: crmDetails.auth_details?.api_key || '',
          api_endpoint: crmDetails.auth_details?.api_endpoint || '',
        },
        custom_fields: {
          appointment_type_id:
            crmDetails.custom_fields?.appointment_type_id || '',
        },
      };

      // Use setTimeout to ensure the form is properly initialized before setting values
      setTimeout(() => {
        form.reset(formValues);
        // Force the select to show the correct value by triggering a re-render
        form.setValue('name', formValues.name, { shouldValidate: true });
      }, 0);
    } else {
      // Reset to default values when no CRM details are present
      const defaultValues = {
        name: 'CLINIKO' as const,
        auth_details: {
          api_key: '',
          api_endpoint: '',
        },
        custom_fields: {
          appointment_type_id: '',
        },
      };

      setTimeout(() => {
        form.reset(defaultValues);
        form.setValue('name', defaultValues.name, { shouldValidate: true });
      }, 0);
    }
  }, [selectedClinic, form]);

  const onSubmit = async (values: CRMDetailsFormValues) => {
    if (!selectedClinic) {
      toast.error('No clinic selected');
      return;
    }

    setLoading(true);

    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      const updatedClinic = {
        ...selectedClinic,
        crm_details: values,
      };

      const result = await createOrUpdateClinicForAdmin(
        updatedClinic,
        accessToken,
        idToken,
      );

      if (result.ok && result.data) {
        toast.success('CRM details updated successfully');
        setSelectedClinic(result.data);
      } else {
        toast.error(result.error || 'Failed to update CRM details');
      }
    } catch (error) {
      console.error('Error updating CRM details:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>CRM Integration Details</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CRM Provider</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    key={field.value} // Force re-render when value changes
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a CRM provider">
                          {field.value === 'CLINIKO' && 'Cliniko'}
                          {field.value === 'GENIE' && 'Genie'}
                          {!field.value && (
                            <span className="text-muted-foreground">
                              Select a CRM provider
                            </span>
                          )}
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CLINIKO">Cliniko</SelectItem>
                      <SelectItem value="GENIE">Genie</SelectItem>
                      {/* <SelectItem value="ANOTHER">Another</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Auth Details</h3>

              <FormField
                control={form.control}
                name="auth_details.api_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter API key"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="auth_details.api_endpoint"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Endpoint (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://api.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Custom Fields</h3>

              <FormField
                control={form.control}
                name="custom_fields.appointment_type_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Appointment Type ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter appointment type ID"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update CRM Details'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
