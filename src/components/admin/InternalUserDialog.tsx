'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

interface AddInternalUserDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: {
    email: string;
    role: 'Owner' | 'Editor' | 'Viewer';
  }) => void;
}

export default function AddInternalUserDialog({
  open,
  onClose,
  onSubmit,
}: AddInternalUserDialogProps) {
  const [email, setEmail] = useState('');
  const [selectedRole, setSelectedRole] = useState<
    'Owner' | 'Editor' | 'Viewer'
  >('Owner');

  const handleSubmit = () => {
    if (!email) return;
    onSubmit({ email, role: selectedRole });
    setEmail('');
    setSelectedRole('Owner');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 text-white border-zinc-700 rounded-lg">
        <DialogHeader>
          <DialogTitle className="text-xl">Add Internal User</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Add a new user and define their access level.
          </p>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <Input
            placeholder="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-zinc-800 text-white border border-zinc-600 focus-visible:ring-0"
          />

          <div className="flex justify-between gap-2">
            {['Owner', 'Editor', 'Viewer'].map((role) => (
              <Button
                key={role}
                variant={selectedRole === role ? 'default' : 'outline'}
                className={` ${selectedRole === role ? 'bg-red-600 text-white' : ''}`}
                onClick={() => {
                  setSelectedRole(role as 'Owner' | 'Editor' | 'Viewer');
                }}
              >
                {role}
              </Button>
            ))}
          </div>

          <div className="flex justify-end gap-2 mt-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>Add User</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
