import { useState } from 'react';
import { <PERSON>, ShieldAlert, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { changeUserRole } from '@/services/userService';

interface ChangeRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff: {
    id: number;
    name: string;
    currentRole: string;
  } | null;
  onRoleChange: (newRole: string) => void;
  userId: string;
  clinicId: string;
  accessToken: string;
  xidToken: string;
}

export function ChangeRoleDialog({
  open,
  onOpenChange,
  staff,
  onRoleChange,
  userId,
  clinicId,
  accessToken,
  xidToken,
}: ChangeRoleDialogProps) {
  const [selectedRole, setSelectedRole] = useState(staff?.currentRole || '');
  const [isLoading, setIsLoading] = useState(false);

  if (!staff) return null;

  const roles = [
    {
      id: 'master',
      name: 'Master',
      description: 'Full access to all features and settings',
      icon: ShieldAlert,
      iconColor: 'text-rose-500',
    },
    {
      id: 'staff',
      name: 'Staff',
      description: 'Limited access to features based on permissions',
      icon: Shield,
      iconColor: 'text-blue-500',
    },
  ];

  const handleRoleChange = (roleId: string) => {
    setSelectedRole(roleId);
  };

  const handleSave = async () => {
    if (!selectedRole || selectedRole === staff?.currentRole) return;

    setIsLoading(true);
    try {
      const result = await changeUserRole(
        userId,
        clinicId,
        selectedRole,
        accessToken,
        xidToken,
      );

      if (!result.ok) {
        throw new Error(result.error || 'Failed to update role');
      }

      toast.success('Role updated successfully');
      onRoleChange(selectedRole);
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update role',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Change Role
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Update the role for {staff.name}
          </p>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <div className="space-y-2">
            {roles.map((role) => {
              const Icon = role.icon;
              const isSelected = selectedRole === role.id;

              return (
                <button
                  key={role.id}
                  type="button"
                  className={`w-full text-left p-4 rounded-lg border transition-colors ${
                    isSelected
                      ? 'border-cyan-200 bg-cyan-50 dark:border-cyan-900 dark:bg-cyan-950/50'
                      : 'border-border hover:bg-accent'
                  }`}
                  onClick={() => handleRoleChange(role.id)}
                >
                  <div className="flex items-center">
                    <div
                      className={`p-2 rounded-full mr-3 ${isSelected ? 'bg-cyan-100 dark:bg-cyan-900' : 'bg-muted'}`}
                    >
                      <Icon className={`h-5 w-5 ${role.iconColor}`} />
                    </div>
                    <div>
                      <div className="font-medium">{role.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {role.description}
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          <div className="rounded-lg bg-blue-50 p-4 text-sm text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
            <div className="flex">
              <Info className="mr-2 h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>
                Changing the role will update the staff member&apos;s access
                permissions immediately.
              </span>
            </div>
          </div>

          <div className="flex justify-end pt-2">
            <Button
              variant="destructive"
              onClick={handleSave}
              disabled={
                !selectedRole || selectedRole === staff.currentRole || isLoading
              }
            >
              {isLoading ? 'Updating...' : 'Update Role'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
