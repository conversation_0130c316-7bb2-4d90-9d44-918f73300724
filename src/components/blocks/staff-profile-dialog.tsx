import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Shield, ShieldAlert } from 'lucide-react';

interface StaffProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff: {
    id: number;
    name: string;
    email: string;
    role: string;
    status: 'active' | 'pending';
    joinedDate: string;
  } | null;
}

export function StaffProfileDialog({
  open,
  onOpenChange,
  staff,
}: StaffProfileDialogProps) {
  if (!staff) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="relative">
          <DialogTitle className="text-xl font-semibold">
            Staff Profile
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            View detailed information about the staff member
          </p>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex flex-col items-center">
            <Avatar className="h-24 w-24">
              <AvatarImage
                src={`https://xvatar.vercel.app/api/avatar/${staff.email}.svg?rounded=120&size=240`}
                alt={staff.name}
              />
              <AvatarFallback>
                {staff.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <h3 className="mt-4 text-lg font-medium">{staff.name}</h3>
            <p className="text-sm text-muted-foreground">{staff.email}</p>
          </div>

          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Role
                </p>
                <div className="mt-1 flex items-center">
                  {staff.role === 'master' ? (
                    <ShieldAlert className="mr-2 h-5 w-5 text-amber-500" />
                  ) : (
                    <Shield className="mr-2 h-5 w-5 text-blue-500" />
                  )}
                  <span>{staff.role === 'master' ? 'Master' : 'Staff'}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Joined Date
                </p>
                <p className="mt-1">{staff.joinedDate}</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
