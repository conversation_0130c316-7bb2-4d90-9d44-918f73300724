'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronRight, Trash2, Plus, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FormData {
  clinicName: string;
  clinicDescription: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  email: string;
  phone: string;
  website: string;
  services: string[];
  specialties: Array<{
    name: string;
    referralRequired: boolean;
  }>;
  crmSystem: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const steps = [
  {
    id: 1,
    title: 'Welcome to Smart Reception',
    subtitle: "Let's set up your clinic profile to get started",
    fields: ['clinicName', 'clinicDescription'],
  },
  {
    id: 2,
    title: 'Clinic Address',
    subtitle: 'Where is your clinic located?',
    fields: ['address', 'city', 'state', 'zipCode'],
  },
  {
    id: 3,
    title: 'Contact Details',
    subtitle: 'Email, phone & website',
    fields: ['email', 'phone', 'website'],
  },
  {
    id: 4,
    title: 'Services Offered',
    subtitle: 'What services do you provide?',
    fields: ['services'],
  },
  {
    id: 5,
    title: 'Diagnostic Services',
    subtitle: 'Select diagnostic services',
    fields: ['specialties'],
  },
  {
    id: 6,
    title: 'CRM Integration',
    subtitle: 'Connect your CRM system',
    fields: ['crmSystem'],
  },
];

const serviceOptions = [
  'General Consultation',
  'Diagnostic Imaging',
  'Laboratory Services',
  'Physical Therapy',
  'Mental Health Services',
  'Preventive Care',
];

const specialtyOptions = [
  'Cardiologist',
  'Neurologist',
  'Orthopedist',
  'Dermatologist',
  'Pediatrician',
  'Psychiatrist',
];

const crmOptions = [
  'Salesforce',
  'HubSpot',
  'Pipedrive',
  'Zoho CRM',
  'Microsoft Dynamics',
  'Custom Solution',
];

export default function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    clinicName: '',
    clinicDescription: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    email: '',
    phone: '',
    website: '',
    services: [],
    specialties: [],
    crmSystem: '',
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isAnimating, setIsAnimating] = useState(false);

  const validateStep = (step: number): boolean => {
    const newErrors: ValidationErrors = {};
    const currentStepData = steps.find((s) => s.id === step);

    if (!currentStepData) return false;

    currentStepData.fields.forEach((field) => {
      switch (field) {
        case 'clinicName':
          if (!formData.clinicName.trim()) {
            newErrors.clinicName = 'Clinic name is required';
          }
          break;
        case 'address':
          if (!formData.address.trim()) {
            newErrors.address = 'Address is required';
          }
          break;
        case 'city':
          if (!formData.city.trim()) {
            newErrors.city = 'City is required';
          }
          break;
        case 'state':
          if (!formData.state.trim()) {
            newErrors.state = 'State is required';
          }
          break;
        case 'zipCode':
          if (!formData.zipCode.trim()) {
            newErrors.zipCode = 'ZIP code is required';
          } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipCode)) {
            newErrors.zipCode = 'Invalid ZIP code format';
          }
          break;
        case 'email':
          if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Invalid email format';
          }
          break;
        case 'phone':
          if (!formData.phone.trim()) {
            newErrors.phone = 'Phone number is required';
          } else if (
            !/^[+]?[1-9][\d]{0,15}$/.test(
              formData.phone.replace(/[\s\-$$$$]/g, ''),
            )
          ) {
            newErrors.phone = 'Invalid phone number format';
          }
          break;
        case 'services':
          if (formData.services.length === 0) {
            newErrors.services = 'Please select at least one service';
          }
          break;
        case 'specialties':
          if (formData.specialties.length === 0) {
            newErrors.specialties = 'Please add at least one specialty';
          }
          break;
        case 'crmSystem':
          if (!formData.crmSystem) {
            newErrors.crmSystem = 'Please select a CRM system';
          }
          break;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep((prev) => Math.min(prev + 1, steps.length));
        setIsAnimating(false);
      }, 150);
    }
  };

  const handleBack = () => {
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentStep((prev) => Math.max(prev - 1, 1));
      setIsAnimating(false);
    }, 150);
  };

  const handleSubmit = () => {
    if (validateStep(currentStep)) {
      alert('Form submitted successfully!');
    }
  };

  const updateFormData = (
    field: keyof FormData,
    value: FormData[keyof FormData],
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const addSpecialty = () => {
    setFormData((prev) => ({
      ...prev,
      specialties: [...prev.specialties, { name: '', referralRequired: false }],
    }));
  };

  const updateSpecialty = (
    index: number,
    field: 'name' | 'referralRequired',
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      specialties: prev.specialties.map((specialty, i) =>
        i === index ? { ...specialty, [field]: value } : specialty,
      ),
    }));
  };

  const removeSpecialty = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      specialties: prev.specialties.filter((_, i) => i !== index),
    }));
  };

  const currentStepData = steps.find((step) => step.id === currentStep);
  const progress = (currentStep / steps.length) * 100;

  return (
    <Card className="w-full max-w-2xl mx-auto bg-card border-border">
      <CardContent className="p-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-foreground">
                {currentStepData?.title}
              </h1>
              <p className="text-muted-foreground">
                {currentStepData?.subtitle}
              </p>
            </div>

            {/* Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>
                  Step {currentStep} of {steps.length}
                </span>
                <span>{Math.round(progress)}% complete</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div
            className={cn(
              'transition-all duration-300 ease-in-out',
              isAnimating && 'opacity-50 transform translate-x-2',
            )}
          >
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="clinicName">Clinic name *</Label>
                  <Input
                    id="clinicName"
                    value={formData.clinicName}
                    onChange={(e) =>
                      updateFormData('clinicName', e.target.value)
                    }
                    className={cn(errors.clinicName && 'border-destructive')}
                    aria-describedby={
                      errors.clinicName ? 'clinicName-error' : undefined
                    }
                  />
                  {errors.clinicName && (
                    <p
                      id="clinicName-error"
                      className="text-sm text-destructive"
                    >
                      {errors.clinicName}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clinicDescription">
                    Clinic description (optional)
                  </Label>
                  <Textarea
                    id="clinicDescription"
                    value={formData.clinicDescription}
                    onChange={(e) =>
                      updateFormData('clinicDescription', e.target.value)
                    }
                    rows={3}
                  />
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="address">Street Address *</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => updateFormData('address', e.target.value)}
                    className={cn(errors.address && 'border-destructive')}
                  />
                  {errors.address && (
                    <p className="text-sm text-destructive">{errors.address}</p>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => updateFormData('city', e.target.value)}
                      className={cn(errors.city && 'border-destructive')}
                    />
                    {errors.city && (
                      <p className="text-sm text-destructive">{errors.city}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State *</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => updateFormData('state', e.target.value)}
                      className={cn(errors.state && 'border-destructive')}
                    />
                    {errors.state && (
                      <p className="text-sm text-destructive">{errors.state}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="zipCode">ZIP Code *</Label>
                    <Input
                      id="zipCode"
                      value={formData.zipCode}
                      onChange={(e) =>
                        updateFormData('zipCode', e.target.value)
                      }
                      className={cn(errors.zipCode && 'border-destructive')}
                    />
                    {errors.zipCode && (
                      <p className="text-sm text-destructive">
                        {errors.zipCode}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Clinic email address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                    className={cn(errors.email && 'border-destructive')}
                  />
                  {errors.email && (
                    <p className="text-sm text-destructive">{errors.email}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Main clinic phone number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    className={cn(errors.phone && 'border-destructive')}
                  />
                  {errors.phone && (
                    <p className="text-sm text-destructive">{errors.phone}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="website">Clinic website (optional)</Label>
                  <Input
                    id="website"
                    type="url"
                    value={formData.website}
                    onChange={(e) => updateFormData('website', e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <Label>Select services offered *</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {serviceOptions.map((service) => (
                      <div
                        key={service}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={service}
                          checked={formData.services.includes(service)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              updateFormData('services', [
                                ...formData.services,
                                service,
                              ]);
                            } else {
                              updateFormData(
                                'services',
                                formData.services.filter((s) => s !== service),
                              );
                            }
                          }}
                        />
                        <Label
                          htmlFor={service}
                          className="text-sm font-normal"
                        >
                          {service}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {errors.services && (
                    <p className="text-sm text-destructive">
                      {errors.services}
                    </p>
                  )}
                </div>
              </div>
            )}

            {currentStep === 5 && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <Label>Specialties *</Label>
                  {formData.specialties.map((specialty, index) => (
                    <div
                      key={index}
                      className="space-y-3 p-4 border border-border rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <Select
                          value={specialty.name}
                          onValueChange={(value) =>
                            updateSpecialty(index, 'name', value)
                          }
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Select specialty" />
                          </SelectTrigger>
                          <SelectContent>
                            {specialtyOptions.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => removeSpecialty(index)}
                          className="shrink-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`referral-${index}`}
                          checked={specialty.referralRequired}
                          onCheckedChange={(checked) =>
                            updateSpecialty(
                              index,
                              'referralRequired',
                              checked as boolean,
                            )
                          }
                        />
                        <Label
                          htmlFor={`referral-${index}`}
                          className="text-sm"
                        >
                          Referral required for this specialty
                        </Label>
                      </div>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addSpecialty}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Another Specialty
                  </Button>
                  {errors.specialties && (
                    <p className="text-sm text-destructive">
                      {errors.specialties}
                    </p>
                  )}
                </div>
              </div>
            )}

            {currentStep === 6 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Select your CRM system *</Label>
                  <Select
                    value={formData.crmSystem}
                    onValueChange={(value) =>
                      updateFormData('crmSystem', value)
                    }
                  >
                    <SelectTrigger
                      className={cn(errors.crmSystem && 'border-destructive')}
                    >
                      <SelectValue placeholder="Select your CRM system" />
                      <ChevronRight className="h-4 w-4 opacity-50" />
                    </SelectTrigger>
                    <SelectContent>
                      {crmOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.crmSystem && (
                    <p className="text-sm text-destructive">
                      {errors.crmSystem}
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 1}
              className="px-8"
            >
              Back
            </Button>

            {currentStep < steps.length ? (
              <Button type="button" onClick={handleNext} className="px-8">
                Next
              </Button>
            ) : (
              <Button type="button" onClick={handleSubmit} className="px-8">
                <Check className="h-4 w-4 mr-2" />
                Complete Setup
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
