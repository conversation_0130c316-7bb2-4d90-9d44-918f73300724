import React from 'react';
import { ThemeToggle } from '../ui/ThemeToggle';

const Header = () => {
  return (
    <header className="fixed w-full py-2 top-0 z-50 mx-auto px-1 md:px-14 flex justify-between items-center bg-background/10 backdrop-blur-lg border-border/30 border-b">
      <div>
        <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
          Smart Reception
        </h1>
      </div>
      <nav className="hidden md:flex space-x-8">
        <a
          href="#features"
          className="text-sm font-medium hover:text-primary transition-colors"
        >
          Features
        </a>
        <a
          href="#testimonials"
          className="text-sm font-medium hover:text-primary transition-colors"
        >
          Testimonials
        </a>
        <a
          href="#pricing"
          className="text-sm font-medium hover:text-primary transition-colors"
        >
          Pricing
        </a>
      </nav>
      <div className="flex items-center space-x-4">
        <ThemeToggle />
        <a href="mailto:<EMAIL>">
          <button className="bg-primary/10 text-primary hover:bg-primary/20 font-medium py-2 px-4 rounded-lg transition-colors">
            Contact Us
          </button>
        </a>
      </div>
    </header>
  );
};

export default Header;
