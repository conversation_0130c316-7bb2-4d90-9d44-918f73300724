'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { Save, Loader2, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form } from '@/components/ui/form';

import { ClinicType } from '@/lib/types';

import { BasicInformationCard } from './BasicInformationCard';
import { ClinicAddressesCard } from './ClinicAddressesCard';
import { SystemInformationCard } from './SystemInformationCard';
import { ClinicFormValues, clinicFormSchema } from '@/lib/clinic-form-schema';

interface DiagnosticService {
  name: string;
  is_referral_required: boolean;
}

interface ClinicProfileFormProps {
  selectedClinic: ClinicType;
  onSubmitHandler: (values: ClinicFormValues) => Promise<void>;
  showDiagnosticServices?: boolean;
}

export function ClinicProfileForm({
  selectedClinic,
  onSubmitHandler,
  showDiagnosticServices = false,
}: ClinicProfileFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof clinicFormSchema>>({
    resolver: zodResolver(clinicFormSchema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_website: '',
      clinic_phone: '',
      human_transfer_destination_number: '',
      clinic_addresses: [],
      diagnostic_services: [],
    },
  });

  const handleAddDiagnosticService = (service: DiagnosticService) => {
    appendDiagnosticService(service);

    setTimeout(() => {
      form.trigger('diagnostic_services');
    }, 0);
  };

  const handleRemoveDiagnosticService = (index: number) => {
    removeDiagnosticService(index);
  };

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'clinic_addresses',
  });

  const {
    fields: diagnosticServiceFields,
    append: appendDiagnosticService,
    remove: removeDiagnosticService,
  } = useFieldArray({
    control: form.control,
    name: 'diagnostic_services',
  });

  // Update form when selectedClinic changes
  useEffect(() => {
    if (selectedClinic) {
      const addresses =
        selectedClinic.clinic_addresses?.map((addr) => ({
          address: typeof addr === 'string' ? addr : addr.address || '',
          full_address:
            typeof addr === 'string'
              ? addr
              : addr.full_address || addr.address || '',
          business_location_id:
            typeof addr === 'string' ? '' : addr.business_location_id || '',
        })) || [];

      const diagnosticServices = Array.isArray(
        selectedClinic.diagnostic_services,
      )
        ? selectedClinic.diagnostic_services
        : [];

      const formValues = {
        clinic_name: selectedClinic.clinic_name,
        clinic_email: selectedClinic.clinic_email,
        clinic_website: selectedClinic.clinic_website,
        clinic_phone: selectedClinic.clinic_phone || '',
        human_transfer_destination_number:
          selectedClinic.human_transfer_destination_number || '',
        clinic_addresses: addresses,
        diagnostic_services: diagnosticServices,
      };

      setTimeout(() => {
        form.reset(formValues);
      }, 0);

      setError(null);
    }
  }, [selectedClinic]); // eslint-disable-line react-hooks/exhaustive-deps

  const onSubmit = async () => {
    setLoading(true);
    setError(null);
    try {
      const isValid = await form.trigger();

      if (!isValid) {
        setLoading(false);
        return;
      }

      await new Promise((resolve) => setTimeout(resolve, 0));
      const currentValues = form.getValues();

      await onSubmitHandler(currentValues);
    } catch (err) {
      console.error(err);
      setError('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            onSubmit();
          }}
          className="space-y-6"
        >
          <BasicInformationCard form={form} />
          <ClinicAddressesCard
            form={form}
            fields={fields}
            append={append}
            remove={remove}
          />
          <SystemInformationCard
            selectedClinic={selectedClinic}
            diagnosticServices={diagnosticServiceFields}
            onAddDiagnosticService={handleAddDiagnosticService}
            onRemoveDiagnosticService={handleRemoveDiagnosticService}
            showDiagnosticServices={showDiagnosticServices}
          />
          {selectedClinic.user_clinic_role === 'master' && (
            <div className="flex justify-end">
              <Button type="submit" disabled={loading} size="lg">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}
