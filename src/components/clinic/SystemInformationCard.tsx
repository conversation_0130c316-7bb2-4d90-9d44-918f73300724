'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { ClinicType } from '@/lib/types';
import { DiagnosticServicesTable } from './DiagnosticServicesTable';

interface DiagnosticService {
  name: string;
  is_referral_required: boolean;
}

interface SystemInformationCardProps {
  selectedClinic: ClinicType;
  diagnosticServices: DiagnosticService[];
  onAddDiagnosticService: (service: DiagnosticService) => void;
  onRemoveDiagnosticService: (index: number) => void;
  showDiagnosticServices?: boolean; // New prop to control visibility
}

export function SystemInformationCard({
  selectedClinic,
  diagnosticServices,
  onAddDiagnosticService,
  onRemoveDiagnosticService,
  showDiagnosticServices = false, // Default to false (hidden)
}: SystemInformationCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>System Information</CardTitle>
        <CardDescription>
          Read-only system generated information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <FormLabel>Created At</FormLabel>
            <div className="mt-2">
              <Input
                value={new Date(selectedClinic.created_at).toLocaleDateString()}
                disabled
              />
            </div>
          </div>

          <div>
            <FormLabel>Updated At</FormLabel>
            <div className="mt-2">
              <Input
                value={new Date(selectedClinic.updated_at).toLocaleDateString()}
                disabled
              />
            </div>
          </div>
        </div>

        <div>
          <FormLabel>Clinic ID</FormLabel>
          <div className="mt-2">
            <Input
              value={selectedClinic._id}
              disabled
              className="font-mono text-sm"
            />
          </div>
        </div>

        {/* Only show diagnostic services when explicitly enabled */}
        {showDiagnosticServices && (
          <DiagnosticServicesTable
            services={diagnosticServices}
            onAddService={onAddDiagnosticService}
            onRemoveService={onRemoveDiagnosticService}
            showAddButton={true}
            showDeleteButton={true}
            title="Diagnostic Services"
            description="Services offered by this clinic"
          />
        )}
      </CardContent>
    </Card>
  );
}
