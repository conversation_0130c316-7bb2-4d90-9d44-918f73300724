'use client';

import {
  UseFormReturn,
  UseFieldArrayAppend,
  FieldArrayWithId,
  UseFieldArrayRemove,
} from 'react-hook-form';
import { Plus, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ClinicFormValues } from '@/lib/clinic-form-schema';

interface ClinicAddressesCardProps {
  form: UseFormReturn<ClinicFormValues>;
  fields: FieldArrayWithId<ClinicFormValues, 'clinic_addresses', 'id'>[];
  append: UseFieldArrayAppend<ClinicFormValues, 'clinic_addresses'>;
  remove: UseFieldArrayRemove;
}

export function ClinicAddressesCard({
  form,
  fields,
  append,
  remove,
}: ClinicAddressesCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Clinic Addresses</CardTitle>
        <CardDescription>
          Manage your clinic locations and addresses
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {fields.map((field, index) => (
          <div
            key={field.id}
            className="space-y-4 p-4 border rounded-lg bg-muted/30"
          >
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-sm">Address {index + 1}</h4>
              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => remove(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name={`clinic_addresses.${index}.address`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Short Address</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Hampton Park" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`clinic_addresses.${index}.business_location_id`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Business Location ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Optional location identifier"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name={`clinic_addresses.${index}.full_address`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., 127-129 Somerville Road Hampton Park, VIC 3976"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          onClick={() =>
            append({
              address: '',
              full_address: '',
              business_location_id: '',
            })
          }
          className="w-full"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add New Address
        </Button>
      </CardContent>
    </Card>
  );
}
