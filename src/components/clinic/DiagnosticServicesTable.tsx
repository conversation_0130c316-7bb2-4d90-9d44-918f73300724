'use client';

import { Check, X, Plus, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { FormLabel } from '@/components/ui/form';
import { AddDiagnosticServiceDialog } from './AddDiagnosticServiceDialog';

interface DiagnosticService {
  name: string;
  is_referral_required: boolean;
}

interface DiagnosticServicesTableProps {
  services: DiagnosticService[];
  onAddService?: (service: DiagnosticService) => void;
  onRemoveService?: (index: number) => void;
  showAddButton?: boolean;
  showDeleteButton?: boolean;
  title?: string;
  description?: string;
}

export function DiagnosticServicesTable({
  services,
  onAddService,
  onRemoveService,
  showAddButton = false,
  showDeleteButton = false,
  title = 'Diagnostic Services',
  description,
}: DiagnosticServicesTableProps) {
  if (!services || services.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <FormLabel className="text-base font-medium">{title}</FormLabel>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          {showAddButton && onAddService && (
            <AddDiagnosticServiceDialog onAddService={onAddService} />
          )}
        </div>
        <div className="text-center py-8 text-muted-foreground">
          <p>No diagnostic services configured</p>
          {showAddButton && onAddService && (
            <AddDiagnosticServiceDialog
              onAddService={onAddService}
              trigger={
                <Button type="button" variant="outline" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Service
                </Button>
              }
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <FormLabel className="text-base font-medium">{title}</FormLabel>
          {description && (
            <p className="text-sm text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        {showAddButton && onAddService && (
          <AddDiagnosticServiceDialog onAddService={onAddService} />
        )}
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-semibold">Service Name</TableHead>
              <TableHead className="font-semibold text-center w-48">
                Referral Required
              </TableHead>
              {showDeleteButton && onRemoveService && (
                <TableHead className="font-semibold text-center w-24">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {services.map((service, index) => (
              <TableRow key={index} className="hover:bg-muted/30">
                <TableCell className="font-medium py-4">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0" />
                    <span className="text-sm">{service.name}</span>
                  </div>
                </TableCell>
                <TableCell className="text-center py-4">
                  <div className="flex items-center justify-center">
                    {service.is_referral_required ? (
                      <Badge variant="pending">
                        <Check className="h-3 w-3 mr-1" /> Required
                      </Badge>
                    ) : (
                      <Badge variant="success">
                        <X className="h-3 w-3 mr-1" /> Not Required
                      </Badge>
                    )}
                  </div>
                </TableCell>
                {showDeleteButton && onRemoveService && (
                  <TableCell className="text-center py-4">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveService(index)}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="text-xs text-muted-foreground">
        <p>
          <span className="font-medium">{services.length}</span> diagnostic
          service
          {services.length !== 1 ? 's' : ''} configured
        </p>
      </div>
    </div>
  );
}
