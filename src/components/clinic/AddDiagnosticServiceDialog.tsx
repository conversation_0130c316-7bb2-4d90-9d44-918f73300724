'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';

const diagnosticServiceSchema = z.object({
  name: z.string().min(1, 'Service name is required'),
  is_referral_required: z.boolean(),
});

interface DiagnosticService {
  name: string;
  is_referral_required: boolean;
}

interface AddDiagnosticServiceDialogProps {
  onAddService: (service: DiagnosticService) => void;
  trigger?: React.ReactNode;
}

export function AddDiagnosticServiceDialog({
  onAddService,
  trigger,
}: AddDiagnosticServiceDialogProps) {
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof diagnosticServiceSchema>>({
    resolver: zodResolver(diagnosticServiceSchema),
    defaultValues: {
      name: '',
      is_referral_required: false,
    },
  });

  const onSubmit = (values: z.infer<typeof diagnosticServiceSchema>) => {
    onAddService(values);
    form.reset();
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button type="button" variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Service
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Diagnostic Service</DialogTitle>
          <DialogDescription>
            Add a new diagnostic service offered by this clinic.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., X-Ray, Blood Test, MRI"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="is_referral_required"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Referral Required</FormLabel>
                    <FormDescription>
                      Check this if the service requires a referral from a
                      physician.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit">Add Service</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
