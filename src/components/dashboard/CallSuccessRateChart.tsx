'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { CallTypeData } from '@/lib/types';

const callTypeChartConfig = {
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

interface CallSuccessRateChartProps {
  data: CallTypeData[];
  loading: boolean;
}

export function CallSuccessRateChart({
  data,
  loading,
}: CallSuccessRateChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Success Rate (30 Days)</CardTitle>
          <CardDescription>
            Distribution of successful vs unsuccessful calls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.value === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Success Rate (30 Days)</CardTitle>
          <CardDescription>
            Distribution of successful vs unsuccessful calls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No call data"
            description="No call success data available for the last 30 days."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Call Success Rate (30 Days)</CardTitle>
        <CardDescription>
          Distribution of successful vs unsuccessful calls
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={callTypeChartConfig}
          className="mx-auto max-h-72"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={70}
              labelLine={false}
              label={({ name, percent }) =>
                `${name} ${(percent * 100).toFixed(0)}%`
              }
            >
              {data.map((entry) => (
                <Cell key={entry.name} fill={entry.fill} />
              ))}
            </Pie>
            <Legend />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
