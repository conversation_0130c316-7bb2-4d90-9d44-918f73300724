'use client';

import { AreaChart, Area, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { ProcessedCallData } from '@/lib/types';

const formatXAxisDate = (tickItem: string) => {
  if (!tickItem || !tickItem.includes('/')) return tickItem;

  const [month, day] = tickItem.split('/');
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  const monthIndex = parseInt(month) - 1;
  const monthName = monthNames[monthIndex] || month;

  return `${day} ${monthName}`;
};

const callVolumeChartConfig = {
  calls: {
    label: 'Total Calls',
    color: 'hsl(217, 91%, 60%)', // Blue
  },
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

interface MonthlyCallTrendChartProps {
  data: ProcessedCallData[];
  loading: boolean;
}

export function MonthlyCallTrendChart({
  data,
  loading,
}: MonthlyCallTrendChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Call Trend</CardTitle>
          <CardDescription>
            Volume of calls over the last 30 days.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton height="h-[400px]" />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Call Trend</CardTitle>
          <CardDescription>
            Volume of calls over the last 30 days.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No monthly data"
            description="No call data available for the last 30 days."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Call Trend</CardTitle>
        <CardDescription>
          Volume of calls over the last 30 days.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={callVolumeChartConfig}
          className="h-[400px] w-full"
        >
          <AreaChart
            accessibilityLayer
            data={data}
            margin={{ left: 12, right: 12, top: 5, bottom: 0 }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={formatXAxisDate}
            />
            <YAxis allowDecimals={false} tickMargin={8} />
            <ChartTooltip content={<ChartTooltipContent indicator="line" />} />
            <defs>
              <linearGradient id="fillCalls" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-calls)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-calls)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <Area
              dataKey="calls"
              type="natural"
              fill="url(#fillCalls)"
              stroke="var(--color-calls)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
