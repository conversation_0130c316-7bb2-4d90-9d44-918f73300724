'use client';

import { Building2, Clock, Phone, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MetricCardSkeleton } from '@/components/blocks/analytics-skeletons';
import type { IListCallsApiData } from '@/lib/types';

interface DashboardMetricCardsProps {
  analyticsData: IListCallsApiData | null;
  analyticsLoading: boolean;
  clinicsCount: number;
}

export function DashboardMetricCards({
  analyticsData,
  analyticsLoading,
  clinicsCount,
}: DashboardMetricCardsProps) {
  if (analyticsLoading) {
    return (
      <>
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </>
    );
  }

  const totalCalls = analyticsData?.calls?.length || 0;
  const totalCallDurationsInMinutes =
    analyticsData?.summary?.total_duration_in_minutes ?? 0;

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
          <Phone className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalCalls}</div>
          <p className="text-xs text-muted-foreground">Last 30 days</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Call Durations
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {totalCallDurationsInMinutes} mins
          </div>
          <p className="text-xs text-muted-foreground">Last 30 days</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Avg. Call Duration
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {analyticsData?.summary
              ? `${analyticsData.summary.average_duration_in_minutes.toFixed(1)} mins`
              : '0 mins'}
          </div>
          <p className="text-xs text-muted-foreground">Last 30 days</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Clinics</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{clinicsCount}</div>
          <p className="text-xs text-muted-foreground">Active clinics</p>
        </CardContent>
      </Card>
    </>
  );
}
