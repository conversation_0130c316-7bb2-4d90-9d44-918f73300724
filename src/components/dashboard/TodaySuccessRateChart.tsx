'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { CallTypeData } from '@/lib/types';

const callTypeChartConfig = {
  successful: {
    label: 'Successful',
    color: 'hsl(142, 76%, 36%)', // Green
  },
  unsuccessful: {
    label: 'Unsuccessful',
    color: 'hsl(0, 84%, 60%)', // Red
  },
} satisfies ChartConfig;

interface TodaySuccessRateChartProps {
  data: CallTypeData[];
  loading: boolean;
  todayCallsCount: number;
}

export function TodaySuccessRateChart({
  data,
  loading,
  todayCallsCount,
}: TodaySuccessRateChartProps) {
  if (loading) {
    return (
      <Card className="lg:col-span-3">
        <CardHeader>
          <CardTitle>Today&apos;s Success Rate (24h)</CardTitle>
          <CardDescription>
            Successful vs. unsuccessful calls in the last 24 hours.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (todayCallsCount === 0) {
    return (
      <Card className="lg:col-span-3">
        <CardHeader>
          <CardTitle>Today&apos;s Success Rate (24h)</CardTitle>
          <CardDescription>
            Successful vs. unsuccessful calls in the last 24 hours.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No calls today"
            description="No call data available for the last 24 hours."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="lg:col-span-3">
      <CardHeader>
        <CardTitle>Today&apos;s Success Rate (24h)</CardTitle>
        <CardDescription>
          Successful vs. unsuccessful calls in the last 24 hours.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={callTypeChartConfig}
          className="mx-auto max-h-72"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={80}
              labelLine={false}
              label={({ name, percent, value }) =>
                value > 0 ? `${name} ${(percent * 100).toFixed(0)}%` : ''
              }
            >
              {data.map((entry) => (
                <Cell key={entry.name} fill={entry.fill} />
              ))}
            </Pie>
            <Legend />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
