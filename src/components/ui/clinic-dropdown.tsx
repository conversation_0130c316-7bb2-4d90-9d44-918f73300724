'use client';

import { Building2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useClinic } from '@/contexts/ClinicContext';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ClinicDropdownProps {
  className?: string;
}

export function ClinicDropdown({ className }: ClinicDropdownProps) {
  const { clinics, selectedClinic, setSelectedClinic, loading } = useClinic();

  const handleValueChange = (value: string) => {
    const clinic = clinics.find((c) => c._id === value);
    if (clinic) {
      setSelectedClinic(clinic);
    }
  };

  if (loading) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <Skeleton className="h-8 w-48" />
      </div>
    );
  }

  if (clinics.length === 0) {
    return (
      <div
        className={cn(
          'flex items-center gap-2 text-muted-foreground',
          className,
        )}
      >
        <Building2 className="h-4 w-4" />
        <span className="text-sm">No clinics available</span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Building2 className="h-4 w-4 text-muted-foreground" />
      <Select
        value={selectedClinic?._id || ''}
        onValueChange={handleValueChange}
      >
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Select clinic..." />
        </SelectTrigger>
        <SelectContent>
          {clinics.map((clinic) => (
            <SelectItem key={clinic._id} value={clinic._id}>
              {clinic.clinic_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
