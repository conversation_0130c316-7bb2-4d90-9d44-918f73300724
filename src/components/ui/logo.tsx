'use client';

import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import darkLogo from '@/assets/logo-dark.png';
import lightLogo from '@/assets/logo-light.png';

export function Logo() {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null; // avoid hydration mismatch

  const currentTheme = theme === 'system' ? systemTheme : theme;
  const logoSrc = currentTheme === 'dark' ? darkLogo : lightLogo;

  return (
    <Image src={logoSrc} alt="Smart Reception Logo" height={40} priority />
  );
}
