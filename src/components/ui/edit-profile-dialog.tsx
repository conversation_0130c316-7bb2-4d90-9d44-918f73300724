'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { UserProfile } from '@/services/userService';
import { Edit, Loader2 } from 'lucide-react';
import { updateUserProfile } from '@/actions/user';
import { useAuth } from '@/contexts/AuthContext';

const editProfileSchema = z.object({
  first_name: z.string().min(1, { message: 'First name is required' }),
  last_name: z.string().min(1, { message: 'Last name is required' }),
});

type EditProfileFormData = z.infer<typeof editProfileSchema>;

interface EditProfileDialogProps {
  userProfile: UserProfile;
  onProfileUpdated: (updatedProfile: UserProfile) => void;
  trigger?: React.ReactNode;
}

export function EditProfileDialog({
  userProfile,
  onProfileUpdated,
  trigger,
}: EditProfileDialogProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  const form = useForm<EditProfileFormData>({
    resolver: zodResolver(editProfileSchema),
    defaultValues: {
      first_name: userProfile.first_name,
      last_name: userProfile.last_name,
    },
  });

  const onSubmit = async (values: EditProfileFormData) => {
    setIsSubmitting(true);

    try {
      const session = await getTokens(true);
      const accessToken = session.accessToken;
      const xidtoken = session.idToken;

      if (!accessToken || !xidtoken) {
        throw new Error('No access token or id token available');
      }

      const result = await updateUserProfile(accessToken, xidtoken, {
        _id: userProfile._id,
        first_name: values.first_name,
        last_name: values.last_name,
      });

      if (!result.ok) {
        throw new Error(result.error || 'Failed to update profile');
      }

      const updatedProfile = result.data;

      if (updatedProfile) {
        onProfileUpdated(updatedProfile);
      }

      toast.success('Profile Updated', {
        description: 'Your profile has been updated successfully.',
      });

      setOpen(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Update Failed', {
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update profile. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" className="gap-2">
      <Edit className="h-4 w-4" />
      Edit Profile
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogDescription>
            Update your first name and last name. Click save when you&apos;re
            done.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 py-4">
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your first name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your last name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
