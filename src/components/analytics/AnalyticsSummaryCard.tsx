import { ProcessedCallData } from '@/lib/types';
import { Card } from '../ui/card';

interface IAnalyticsSummaryCardProps {
  callData: ProcessedCallData[];
}

const AnalyticsSummaryCard = (props: IAnalyticsSummaryCardProps) => {
  const { callData } = props;
  const totalCalls = callData.reduce((sum, day) => sum + day.calls, 0);
  const totalCallDurationInMs = callData.reduce(
    (sum, day) => sum + day.total_duration_ms,
    0,
  );
  const avgCallDurationInMs = totalCalls
    ? totalCallDurationInMs / totalCalls
    : 0;
  return (
    <Card>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold">{totalCalls}</div>
          <div className="text-sm text-muted-foreground">Total Calls</div>
        </div>
        <div>
          <div className="text-2xl font-bold">
            {(totalCallDurationInMs / 60000).toFixed(1)} mins
          </div>
          <div className="text-sm text-muted-foreground">
            Total Call Duration
          </div>
        </div>
        <div>
          <div className="text-2xl font-bold">
            {(avgCallDurationInMs / 60000).toFixed(1)} mins
          </div>
          <div className="text-sm text-muted-foreground">
            Average Call Duration
          </div>
        </div>
      </div>
    </Card>
  );
};

export default AnalyticsSummaryCard;
