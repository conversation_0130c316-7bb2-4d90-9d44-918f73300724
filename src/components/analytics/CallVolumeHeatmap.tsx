'use client';

import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ChartSkeleton } from '@/components/blocks/analytics-skeletons';

interface HourlyData {
  name: string;
  calls: number;
}

interface CallVolumeHeatmapProps {
  data: HourlyData[];
  loading: boolean;
}

export function CallVolumeHeatmap({ data, loading }: CallVolumeHeatmapProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Call Volume Heatmap</CardTitle>
          <CardDescription>Peak calling hours visualization</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Call Volume Heatmap</CardTitle>
        <CardDescription>Peak calling hours visualization</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                borderRadius: 'var(--radius)',
                backgroundColor: 'var(--card)',
                color: 'var(--card-foreground)',
                border: '1px solid var(--muted)',
              }}
            />
            <Bar dataKey="calls" radius={[4, 4, 0, 0]}>
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    entry.calls > 10
                      ? '#ef4444'
                      : entry.calls > 5
                        ? '#f59e0b'
                        : entry.calls > 2
                          ? '#10b981'
                          : '#3b82f6'
                  }
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
