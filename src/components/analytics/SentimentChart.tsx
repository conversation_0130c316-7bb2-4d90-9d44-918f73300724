'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';

interface SentimentData {
  name: string;
  value: number;
  fill: string;
}

interface SentimentChartProps {
  data: SentimentData[];
  loading: boolean;
}

export function SentimentChart({ data, loading }: SentimentChartProps) {
  const processedData = [data[0], data[1]];

  if (loading) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>User Sentiment Analysis</CardTitle>
          <CardDescription>Customer sentiment distribution</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.value === 0)) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>User Sentiment Analysis</CardTitle>
          <CardDescription>Customer sentiment distribution</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No sentiment data"
            description="No sentiment analysis data available."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle>User Sentiment Analysis</CardTitle>
        <CardDescription>Customer sentiment distribution</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={processedData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) =>
                `${name}: ${(percent * 100).toFixed(0)}%`
              }
              outerRadius={80}
              stroke="var(--muted)"
              dataKey="value"
              fill="#8884d8"
            >
              {processedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
