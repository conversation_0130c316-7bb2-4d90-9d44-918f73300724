'use client';

import { <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
} from '@/components/ui/chart';
import { RetellCallIntent } from '@/lib/types';

interface CallIntentData {
  name: string;
  value: number;
  fill: string;
}

interface CallIntentChartProps {
  data: CallIntentData[];
  loading?: boolean;
  className?: string;
}

const INTENT_COLORS = {
  [RetellCallIntent.book_appointment]: '#FF5733', //  red-orange
  [RetellCallIntent.cancel_appointment]: '#33FF57', //  green
  [RetellCallIntent.reschedule_appointment]: '#3357FF', //  blue
  [RetellCallIntent.general_inquiry]: '#FF33A1', //  pink
  [RetellCallIntent.update_patient_details]: '#FFC733', //  yellow
  [RetellCallIntent.transfer_call]: '#33FFF5', //  cyan
  [RetellCallIntent.others]: '#A133FF', //  purple
};

const INTENT_LABELS = {
  [RetellCallIntent.book_appointment]: 'Book Appointment',
  [RetellCallIntent.cancel_appointment]: 'Cancel Appointment',
  [RetellCallIntent.reschedule_appointment]: 'Reschedule Appointment',
  [RetellCallIntent.general_inquiry]: 'General Inquiry',
  [RetellCallIntent.update_patient_details]: 'Update Patient Details',
  [RetellCallIntent.transfer_call]: 'Transfer Call',
  [RetellCallIntent.others]: 'Others',
};

const chartConfig = {
  calls: {
    label: 'Calls',
  },
  book_appointment: {
    label: 'Book Appointment',
    color: '#FF5733',
  },
  cancel_appointment: {
    label: 'Cancel Appointment',
    color: '#33FF57',
  },
  reschedule_appointment: {
    label: 'Reschedule Appointment',
    color: '#3357FF',
  },
  general_inquiry: {
    label: 'General Inquiry',
    color: '#FF33A1',
  },
  update_patient_details: {
    label: 'Update Patient Details',
    color: '#FFC733',
  },
  transfer_call: {
    label: 'Transfer Call',
    color: '#33FFF5',
  },
  others: {
    label: 'Others',
    color: '#A133FF',
  },
} satisfies ChartConfig;

export function CallIntentChart({
  data,
  loading = false,
  className,
}: CallIntentChartProps) {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Call Intent Distribution</CardTitle>
          <CardDescription>
            Breakdown of call purposes and intents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[350px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalCalls = data.reduce((sum, item) => sum + item.value, 0);

  if (totalCalls === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Call Intent Distribution</CardTitle>
          <CardDescription>
            Breakdown of call purposes and intents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-96 flex items-center justify-center text-muted-foreground">
            No call data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Call Intent Distribution</CardTitle>
        <CardDescription>
          Breakdown of call purposes and intents
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="m-auto py-1 h-full max-h-96"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie data={data} dataKey="value" nameKey="name" />
            <ChartLegend />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

export { INTENT_COLORS, INTENT_LABELS };
