'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';
import type { ProcessedCallData } from '@/lib/types';

interface WeeklyCallChartProps {
  data: ProcessedCallData[];
  loading: boolean;
}

export function WeeklyCallChart({ data, loading }: WeeklyCallChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Weekly Call Trends</CardTitle>
          <CardDescription>
            Call volume trends over the past week
          </CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <ChartSkeleton height="h-[400px]" />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Weekly Call Trends</CardTitle>
          <CardDescription>
            Call volume trends over the past week
          </CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <EmptyState
            title="No weekly data"
            description="No call data available for the past week."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Weekly Call Trends</CardTitle>
        <CardDescription>Call volume trends over the past week</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                borderRadius: 'var(--radius)',
                backgroundColor: 'var(--card)',
                color: 'var(--card-foreground)',
                border: '1px solid var(--muted)',
              }}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="calls"
              name="Total Calls"
              stroke="#6366f1"
              activeDot={{ r: 8 }}
            />
            <Line
              type="monotone"
              dataKey="successful"
              name="Successful"
              stroke="#10b981"
            />
            <Line
              type="monotone"
              dataKey="unsuccessful"
              name="Unsuccessful"
              stroke="#ef4444"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
