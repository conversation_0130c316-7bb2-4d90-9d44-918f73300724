'use client';

import {
  AreaChart,
  Area,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartSkeleton,
  EmptyState,
} from '@/components/blocks/analytics-skeletons';

interface HourlyData {
  name: string;
  calls: number;
}

interface HourlyCallChartProps {
  data: HourlyData[];
  loading: boolean;
}

export function HourlyCallChart({ data, loading }: HourlyCallChartProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Hourly Call Distribution</CardTitle>
          <CardDescription>Call volume by hour of day</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (data.every((item) => item.calls === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Hourly Call Distribution</CardTitle>
          <CardDescription>Call volume by hour of day</CardDescription>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No hourly data"
            description="No call data available for hourly distribution."
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Hourly Call Distribution</CardTitle>
        <CardDescription>Call volume by hour of day</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--muted)" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                borderRadius: 'var(--radius)',
                backgroundColor: 'var(--card)',
                color: 'var(--card-foreground)',
                border: '1px solid var(--muted)',
              }}
            />
            <Area
              type="monotone"
              dataKey="calls"
              className="fill-blue-500/10 stroke-blue-500"
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
