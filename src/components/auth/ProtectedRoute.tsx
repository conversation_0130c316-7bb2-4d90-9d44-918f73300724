'use client';

import { useEffect, ReactNode, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({
  children,
  requireAdmin = false,
}: ProtectedRouteProps) {
  const { isAdmin, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const verifyAuth = async () => {
      // Wait for auth context to finish loading
      if (loading) return;

      try {
        if (!isAuthenticated) {
          router.replace('/login');
          return;
        }

        // If admin access is required but user is not admin
        if (requireAdmin && !isAdmin) {
          router.replace('/dashboard');
          return;
        }

        // If we reach here, user is properly authenticated
        if (isMounted) {
          setIsVerifying(false);
        }
      } catch (error) {
        console.error(
          'ProtectedRoute - Error during auth verification:',
          error,
        );
        router.replace('/login');
      }
    };

    verifyAuth();

    return () => {
      isMounted = false;
    };
  }, [isAdmin, loading, requireAdmin, router, isAuthenticated]);

  // Show loading state while verifying auth
  if (isVerifying || loading) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // If we're still authenticated but not an admin when admin is required
  if (isAuthenticated && requireAdmin && !isAdmin) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  // If authenticated (and admin if required), render the children
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Default fallback (should have been redirected to login already)
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-muted-foreground">Redirecting to login...</p>
      </div>
    </div>
  );
}
