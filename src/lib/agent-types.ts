// Voice Agent Types
export interface VoiceAgent {
  data: {
    agent_id: string;
    channel: 'voice';
    last_modification_timestamp: number;
    agent_name: string;
    response_engine: {
      type: 'retell-llm';
      llm_id: string;
      version: number;
    };
    language: string;
    opt_out_sensitive_data_storage: boolean;
    opt_in_signed_url: boolean;
    version: number;
    is_published: boolean;
    version_title: string;
    post_call_analysis_model: string;
    voice_id: string;
    voice_temperature?: number;
    voice_speed?: number;
    volume: number;
    max_call_duration_ms: number;
    pronunciation_dictionary?: Array<{
      word: string;
      alphabet: 'ipa';
      phoneme: string;
    }>;
    voicemail_detection_timeout_ms: number;
    allow_user_dtmf: boolean;
    user_dtmf_options: Record<string, unknown>;
  };
}

// API Response type
export interface ApiResponse<T> {
  ok: boolean;
  data?: T;
  error?: string;
}

export interface CreateVoiceAgentInput {
  pronunciationDict?: Array<{
    word: string;
    alphabet: 'ipa' | 'cmu';
    phoneme: string;
  }>;
}
