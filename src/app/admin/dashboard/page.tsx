'use client';

import { useState, useEffect, useMemo } from 'react';
import { Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
import type {
  IListCallsApiData,
  Call,
  ProcessedCallData,
  CallTypeData,
  SentimentData,
} from '@/lib/types';
import { useAuth } from '@/contexts/AuthContext';
import { CallIntentChart } from '@/components/analytics/CallIntentChart';
import {
  INTENT_COLORS,
  INTENT_LABELS,
} from '@/components/analytics/CallIntentChart';

import {
  DashboardMetricCards,
  WeeklyCallAnalysisChart,
  MonthlyCallTrendChart,
  CallSuccessRateChart,
  CustomerSentimentChart,
} from '@/components/dashboard';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { fetchAdminAnalyticsData } from '@/actions/admin';

export default function DashboardPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const { selectedClinic, clinics, loading } = useAdminClinic();
  const { getTokens } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<IListCallsApiData | null>(
    null,
  );
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  const processCallsData = useMemo(() => {
    return (calls: Call[], period: 'day' | 'week' | 'month') => {
      if (!calls || calls.length === 0) {
        return [];
      }

      const now = new Date();
      const dataMap = new Map<string, ProcessedCallData>();
      const orderedKeys: string[] = [];

      if (period === 'day') {
        // Last 24 hours, grouped by hour
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i, 0, 0, 0);
          const key = hour.getHours().toString().padStart(2, '0') + ':00';
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            total_duration_ms: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const diffMs = now.getTime() - callDate.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);

          if (diffHours >= 0 && diffHours < 24) {
            const hourKey =
              callDate.getHours().toString().padStart(2, '0') + ':00';
            const data = dataMap.get(hourKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              data.total_duration_ms += call.duration_ms ?? 0;
            }
          }
        });
      } else if (period === 'week') {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: days[date.getDay()],
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            total_duration_ms: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 7) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              data.total_duration_ms += call.duration_ms ?? 0;
            }
          }
        });
      } else {
        // Monthly - last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          date.setHours(0, 0, 0, 0);
          const key = `${date.getMonth() + 1}/${date.getDate()}`;
          orderedKeys.push(key);
          dataMap.set(key, {
            name: key,
            calls: 0,
            successful: 0,
            unsuccessful: 0,
            total_duration_ms: 0,
          });
        }

        calls.forEach((call) => {
          const callDate = new Date(call.start_timestamp);
          if (isNaN(callDate.getTime())) return;

          const callDateStart = new Date(callDate);
          callDateStart.setHours(0, 0, 0, 0);

          const nowStart = new Date(now);
          nowStart.setHours(0, 0, 0, 0);

          const diffMs = nowStart.getTime() - callDateStart.getTime();
          const diffDays = diffMs / (1000 * 60 * 60 * 24);

          if (diffDays >= 0 && diffDays < 30) {
            const dateKey = `${callDate.getMonth() + 1}/${callDate.getDate()}`;
            const data = dataMap.get(dateKey);
            if (data) {
              data.calls += 1;
              data.successful += call.call_analysis?.call_successful ? 1 : 0;
              data.unsuccessful += call.call_analysis?.call_successful ? 0 : 1;
              data.total_duration_ms += call.duration_ms ?? 0;
            }
          }
        });
      }

      return orderedKeys.map((key) => dataMap.get(key)!);
    };
  }, []);

  const processCallTypeData = useMemo(() => {
    return (calls: Call[]): CallTypeData[] => {
      if (!calls || calls.length === 0) {
        return [
          { name: 'Successful', value: 0, fill: 'var(--color-successful)' },
          { name: 'Unsuccessful', value: 0, fill: 'var(--color-unsuccessful)' },
        ];
      }

      const successful = calls.filter(
        (call) => call.call_analysis?.call_successful,
      ).length;
      const unsuccessful = calls.length - successful;

      return [
        {
          name: 'Successful',
          value: successful,
          fill: 'var(--color-successful)',
        },
        {
          name: 'Unsuccessful',
          value: unsuccessful,
          fill: 'var(--color-unsuccessful)',
        },
      ];
    };
  }, []);

  const processSentimentData = useMemo(() => {
    return (calls: Call[]): SentimentData[] => {
      if (!calls || calls.length === 0) return [];

      const sentimentCounts = calls.reduce(
        (acc, call) => {
          const sentiment =
            call.call_analysis?.user_sentiment?.toLowerCase() || 'unknown';
          if (sentiment.includes('positive')) {
            acc.positive += 1;
          } else if (sentiment.includes('negative')) {
            acc.negative += 1;
          } else {
            acc.neutral += 1;
          }
          return acc;
        },
        { positive: 0, neutral: 0, negative: 0 },
      );

      return [
        {
          name: 'Positive',
          value: sentimentCounts.positive,
          fill: 'var(--color-positive)',
        },
        {
          name: 'Neutral',
          value: sentimentCounts.neutral,
          fill: 'var(--color-neutral)',
        },
        {
          name: 'Negative',
          value: sentimentCounts.negative,
          fill: 'var(--color-negative)',
        },
      ].filter((item) => item.value > 0);
    };
  }, []);

  const processCallIntentData = useMemo(() => {
    return (calls: Call[]) => {
      if (!calls || calls.length === 0) return [];

      const intentCounts = {
        [INTENT_LABELS.book_appointment]: 0,
        [INTENT_LABELS.cancel_appointment]: 0,
        [INTENT_LABELS.reschedule_appointment]: 0,
        [INTENT_LABELS.general_inquiry]: 0,
        [INTENT_LABELS.update_patient_details]: 0,
        [INTENT_LABELS.transfer_call]: 0,
        [INTENT_LABELS.others]: 0,
      };

      calls.forEach((call) => {
        const intent = call.custom?.intent;
        if (intent && INTENT_LABELS[intent]) {
          intentCounts[INTENT_LABELS[intent]]++;
        } else {
          intentCounts[INTENT_LABELS.others]++;
        }
      });

      return Object.entries(intentCounts)
        .filter(([, value]) => value > 0)
        .map(([name, value]) => {
          const intentKey = Object.keys(INTENT_LABELS).find(
            (key) => INTENT_LABELS[key as keyof typeof INTENT_LABELS] === name,
          ) as keyof typeof INTENT_COLORS;

          return {
            name,
            value,
            fill: INTENT_COLORS[intentKey] || '#6b7280',
          };
        });
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedClinic?._id) return;

      setAnalyticsLoading(true);
      try {
        const { accessToken, idToken } = await getTokens();
        if (!accessToken || !idToken) {
          console.error('Either access or id token not available');
          setAnalyticsLoading(false);
          return;
        }
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);

        const response = await fetchAdminAnalyticsData(
          accessToken,
          idToken,
          selectedClinic._id,
          startDate.toISOString(),
          endDate.toISOString(),
        );

        if (response.ok && response.data) {
          setAnalyticsData(response.data);
        } else {
          console.error('Failed to fetch analytics data:', response.error);
          setAnalyticsData(null);
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        setAnalyticsData(null);
      } finally {
        setAnalyticsLoading(false);
      }
    };

    fetchData();
  }, [selectedClinic?._id, getTokens]);

  const {
    weeklyCalls,
    monthlyCallData,
    overallCallTypeData,
    sentimentData,
    callIntentData,
  } = useMemo(() => {
    const calls = analyticsData?.calls || [];
    const now = new Date();

    // Today's calls (last 24 hours)
    const today = calls.filter((call) => {
      const callTime = new Date(call.start_timestamp);
      if (isNaN(callTime.getTime())) return false;

      const diffHours = (now.getTime() - callTime.getTime()) / (1000 * 60 * 60);
      return diffHours >= 0 && diffHours < 24;
    });

    // Weekly calls (last 7 days)
    const weeklyFilteredCalls = calls.filter((call) => {
      const callTime = new Date(call.start_timestamp);
      if (isNaN(callTime.getTime())) return false;

      const callDateStart = new Date(callTime);
      callDateStart.setHours(0, 0, 0, 0);

      const nowStart = new Date(now);
      nowStart.setHours(0, 0, 0, 0);

      const diffDays =
        (nowStart.getTime() - callDateStart.getTime()) / (1000 * 60 * 60 * 24);
      return diffDays >= 0 && diffDays < 7;
    });

    return {
      todayCalls: today,
      weeklyCalls: processCallsData(weeklyFilteredCalls, 'week'),
      monthlyCallData: processCallsData(calls, 'month'),
      overallCallTypeData: processCallTypeData(calls), // For the 30-day pie chart
      sentimentData: processSentimentData(calls),
      callIntentData: processCallIntentData(calls),
    };
  }, [
    analyticsData,
    processCallsData,
    processCallTypeData,
    processSentimentData,
    processCallIntentData,
  ]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          {selectedClinic && (
            <p className="text-sm text-muted-foreground mt-1">
              {selectedClinic.clinic_name} • {clinics.length} clinic
              {clinics.length !== 1 ? 's' : ''} total
            </p>
          )}
          {loading && (
            <p className="text-sm text-muted-foreground mt-1">
              Loading clinics...
            </p>
          )}
        </div>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DashboardMetricCards
          analyticsData={analyticsData}
          analyticsLoading={analyticsLoading}
          clinicsCount={clinics.length}
        />
      </div>

      {/* New Chart Layout */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-7">
        <WeeklyCallAnalysisChart
          data={weeklyCalls}
          loading={analyticsLoading}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <MonthlyCallTrendChart
          data={monthlyCallData}
          loading={analyticsLoading}
        />

        <CallIntentChart data={callIntentData} loading={analyticsLoading} />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <CallSuccessRateChart
          data={overallCallTypeData}
          loading={analyticsLoading}
        />
        <CustomerSentimentChart
          data={sentimentData}
          loading={analyticsLoading}
        />
      </div>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
        clinics={selectedClinic ? [selectedClinic] : []}
        isAdminInvite={true}
      />
    </div>
  );
}
