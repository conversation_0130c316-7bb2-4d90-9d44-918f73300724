'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import {
  AlertCircle,
  Loader2,
  Upload,
  Check,
  X,
  File,
  Clock,
  Info,
  RefreshCw,
  Book,
} from 'lucide-react';
import {
  getKnowledgeBases,
  uploadKnowledgeBaseFiles,
} from '@/actions/knowledge-base';
import { fetchAuthSession } from 'aws-amplify/auth';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { KnowledgeBaseApiResponse } from '@/lib/types';
import { format, fromUnixTime } from 'date-fns';

export default function KnowledgeBasePage() {
  const { id } = useParams();
  const [knowledgeBases, setKnowledgeBases] = useState<
    KnowledgeBaseApiResponse['data']
  >([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    async function fetchKnowledgeBases() {
      try {
        setLoading(true);
        setError(null);
        const session = await fetchAuthSession({ forceRefresh: true });
        const token = session.tokens?.accessToken?.toString() || '';
        const clinicId = Array.isArray(id) ? id[0] : id;

        if (!clinicId) {
          setError('Clinic ID is required');
          setLoading(false);
          return;
        }
        const result = await getKnowledgeBases(clinicId, token);

        if (result.ok && result.data) {
          console.log('Knowledge bases:', result.data);
          setKnowledgeBases(
            Array.isArray(result.data.data)
              ? result.data.data
              : [result.data.data],
          );
        } else {
          setError(result.error?.message || 'Failed to fetch knowledge bases');
          setKnowledgeBases([]);
        }
      } catch (err) {
        console.error('Error fetching knowledge bases:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchKnowledgeBases();
  }, [id]);

  const formatTimestamp = (timestamp: number) => {
    try {
      const date =
        timestamp > 9999999999
          ? fromUnixTime(timestamp / 1000)
          : fromUnixTime(timestamp);
      return format(date, 'MMM dd, yyyy HH:mm:ss');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Invalid date';
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const fileList = Array.from(event.target.files);
      setFiles((prevFiles) => [...prevFiles, ...fileList]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const handleUploadFiles = async () => {
    if (files.length === 0) {
      setError('Please select files to upload');
      return;
    }

    try {
      setUploadingFiles(true);
      setSuccess(null);
      setError(null);
      setUploadProgress(0);
      const session = await fetchAuthSession({ forceRefresh: true });
      const token = session.tokens?.accessToken?.toString() || '';
      const clinicId = Array.isArray(id) ? id[0] : id;

      if (!clinicId) {
        setError('Clinic ID is required');
        setUploadingFiles(false);
        return;
      }

      const formData = new FormData();
      files.forEach((file) => {
        formData.append('files', file);
      });

      // Upload files using the specified endpoint
      const result = await uploadKnowledgeBaseFiles(clinicId, token, formData);

      if (result.ok && result.data) {
        setSuccess('Files uploaded successfully');
        setFiles([]);
        // Refresh knowledge bases
        const refreshResult = await getKnowledgeBases(clinicId, token);
        if (refreshResult.ok && refreshResult.data) {
          setKnowledgeBases(
            Array.isArray(refreshResult.data.data)
              ? refreshResult.data.data
              : [refreshResult.data.data],
          );
        }
      } else {
        setError(result.error || 'Failed to upload files');
      }
    } catch (err) {
      console.error('Error uploading files:', err);
      setError('An unexpected error occurred');
    } finally {
      setUploadingFiles(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Base</h1>
          <p className="text-muted-foreground mt-1">
            Manage your clinic&apos;s knowledge bases for voice agents
          </p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="default" className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            {success}
          </AlertDescription>
        </Alert>
      )}

      {/* Existing Knowledge Bases Section */}
      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : knowledgeBases.length > 0 ? (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Existing Knowledge Bases</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {knowledgeBases.map((kb) => (
              <Card key={kb.knowledge_base_id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl truncate break-all">
                      {kb.knowledge_base_name}
                    </CardTitle>
                    <Badge
                      variant={kb.status === 'ready' ? 'default' : 'secondary'}
                    >
                      {kb.status}
                    </Badge>
                  </div>
                  <CardDescription>
                    <div className="flex items-center space-x-2 mt-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>
                        Last Modified:{' '}
                        {formatTimestamp(Number(kb.user_modified_timestamp))}
                      </span>
                    </div>
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Info className="h-4 w-4 text-muted-foreground" />
                    <span>ID: {kb.knowledge_base_id}</span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <RefreshCw className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Auto Refresh:{' '}
                      {kb.enable_auto_refresh ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mt-2">
                      Sources:
                    </h4>
                    <ul className="mt-1 list-disc list-inside text-sm space-y-1">
                      {kb.knowledge_base_sources
                        ? kb.knowledge_base_sources.map((source) => (
                            <li key={source.source_id}>
                              {source.filename ? (
                                <>
                                  <strong>{source.filename}</strong> (
                                  {source.type},{' '}
                                  {(source.file_size! / 1024).toFixed(1)} KB)
                                </>
                              ) : source.title ? (
                                <>
                                  <strong>{source.title}</strong> ({source.type}
                                  )
                                </>
                              ) : (
                                <em>Unknown source</em>
                              )}
                            </li>
                          ))
                        : null}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Book className="h-16 w-16 text-muted-foreground mb-4" />
            <p className="text-xl font-medium text-center">
              No Knowledge Bases Found
            </p>
            <p className="text-muted-foreground text-center mt-1">
              Create your first knowledge base by uploading files below
            </p>
          </CardContent>
        </Card>
      )}

      {/* Upload Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Add Knowledge Base</h2>
        <Card>
          <CardHeader>
            <CardTitle>Upload Documents</CardTitle>
            <CardDescription>
              Upload TXT and PDF files to be included in your knowledge base
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4">
              <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center hover:border-primary transition-colors">
                <label
                  htmlFor="fileUpload"
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-lg font-medium">
                    Click to select files or drag and drop
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Supported file types: PDF, TXT (Max 10MB per file)
                  </p>
                  <input
                    id="fileUpload"
                    type="file"
                    className="hidden"
                    multiple
                    accept=".pdf,.txt"
                    onChange={handleFileChange}
                    aria-label="Upload files"
                  />
                </label>
              </div>

              {files.length > 0 && (
                <div className="mt-2">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Selected Files</h4>
                  </div>
                  <div className="space-y-2">
                    {files.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                      >
                        <div className="flex items-center space-x-3 truncate">
                          <File className="h-5 w-5 text-primary" />
                          <div className="overflow-hidden">
                            <p className="font-medium truncate">{file.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(file.size / 1024).toFixed(2)} KB
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveFile(index)}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {uploadingFiles && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Uploading files...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button
            onClick={handleUploadFiles}
            disabled={uploadingFiles || files.length === 0}
            className="w-full sm:w-auto"
          >
            {uploadingFiles ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Create Knowledge Base
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
