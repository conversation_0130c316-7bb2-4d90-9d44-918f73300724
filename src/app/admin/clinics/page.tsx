'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertCircle,
  Building2,
  Check,
  Clock,
  Edit,
  Globe,
  Loader2,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  Trash2,
  Mail,
} from 'lucide-react';
import { format, parseISO } from 'date-fns';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useAdminClinic } from '@/contexts/AdminClinicContext';
import { createOrUpdateClinicForAdmin } from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';
import { ClinicType } from '@/lib/types';
import { toast } from 'sonner';
import { AddClinicDialog } from '@/components/admin/AddClinicDialog';

export default function ClinicsPage() {
  const router = useRouter();
  const { clinics, setSelectedClinic, loading, refreshClinics } =
    useAdminClinic();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState('all');
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddClinicDialogOpen, setIsAddClinicDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [clinicToDelete, setClinicToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const itemsPerPage = 10;
  const { getTokens } = useAuth();

  const handleDeleteClick = (clinic_id: string, clinic_name: string) => {
    setClinicToDelete({ id: clinic_id, name: clinic_name });
    setIsDeleteDialogOpen(true);
  };

  const deleteClinic = async () => {
    if (!clinicToDelete) return;

    setIsDeleting(true);
    try {
      const { accessToken, idToken } = await getTokens();
      if (!accessToken || !idToken) {
        console.error('Either access or id token is null');
        setIsDeleting(false);
        return;
      }
      const payload: Partial<ClinicType> = {
        _id: clinicToDelete.id,
        is_active: false,
      };
      const result = await createOrUpdateClinicForAdmin(
        payload,
        accessToken,
        idToken,
      );
      if (result.ok && result.data) {
        toast.warning('Clinic deleted successfully');
        refreshClinics();
        setIsDeleteDialogOpen(false);
        setClinicToDelete(null);
      } else {
        setError(result.error || 'Failed to delete clinic');
      }
    } catch (error) {
      setError('Error while deleting the clinic');
      console.error('Failed to delete clinic', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleViewOrEdit = (clinicId: string) => {
    const clinic = clinics.find((c) => c._id === clinicId);
    if (clinic) {
      setSelectedClinic(clinic);
      toast.success(`Selected clinic: ${clinic.clinic_name}`);
      router.push('/admin/clinic');
    } else {
      toast.error('Clinic not found');
    }
  };

  const handleCallAnalytics = (clinicId: string) => {
    const clinic = clinics.find((c) => c._id === clinicId);
    if (clinic) {
      setSelectedClinic(clinic);
      toast.success(`Selected clinic: ${clinic.clinic_name}`);
      router.push('/admin/analytics');
    } else {
      toast.error('Clinic not found');
    }
  };

  const filteredClinics = clinics.filter(
    (clinic) =>
      (currentTab === 'all' ||
        (currentTab === 'active' && clinic.is_active) ||
        (currentTab === 'pending' && !clinic.is_active)) &&
      (clinic.clinic_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        clinic.clinic_email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        clinic.clinic_website
          .toLowerCase()
          .includes(searchQuery.toLowerCase())),
  );

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM dd, yyyy');
    } catch {
      return 'Invalid date';
    }
  };
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const clinicsToDisplay = filteredClinics.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredClinics.length / itemsPerPage);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Clinic Management</h1>
        <div className="mt-2 sm:mt-0">
          <Button size={'lg'} onClick={() => setIsAddClinicDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Clinic
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Clinics</CardTitle>
          <CardDescription>
            {loading ? (
              <span className="flex items-center">
                <Loader2 className="h-3 w-3 animate-spin mr-2" />
                Loading clinics...
              </span>
            ) : error ? (
              <span className="flex items-center text-red-500">
                <AlertCircle className="h-3 w-3 mr-2" />
                {error}
              </span>
            ) : (
              `Manage all registered clinics (${clinics.length} total)`
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  placeholder="Search clinics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-9"
                  disabled={loading}
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-4 shrink-0"
                  disabled={loading}
                >
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
              <Tabs
                value={currentTab}
                onValueChange={setCurrentTab}
                className="w-full sm:w-auto"
              >
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {loading ? (
              <div className="space-y-4 py-4">
                {Array(5)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="flex items-center animate-pulse">
                      <div className="flex h-9 w-9 items-center justify-center rounded-full bg-muted"></div>
                      <div className="ml-4 space-y-2 flex-1">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </div>
                      <div className="ml-auto text-right">
                        <div className="h-4 bg-muted rounded w-16 mb-2"></div>
                        <div className="h-3 bg-muted rounded w-12"></div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => window.location.reload()}
                  >
                    Try Again
                  </Button>
                </AlertDescription>
              </Alert>
            ) : filteredClinics.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">No clinics found</h3>
                <p className="text-muted-foreground">
                  {searchQuery
                    ? 'No clinics match your search criteria'
                    : currentTab !== 'all'
                      ? `No ${currentTab} clinics found`
                      : 'No clinics have been registered yet.'}
                </p>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Clinic</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicsToDisplay.map((clinic) => (
                      <TableRow key={clinic._id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="h-11 w-11 rounded-md bg-primary/10 flex items-center justify-center text-primary">
                              <Building2 className="h-6 w-6" />
                            </div>
                            <div>
                              <Button
                                variant={'link'}
                                className="py-0 px-1 h-8"
                                onClick={() => handleViewOrEdit(clinic._id)}
                              >
                                {clinic.clinic_name}
                              </Button>
                              <div className="text-sm text-muted-foreground flex items-center">
                                <Mail className="h-3 w-3 mr-1" />
                                {clinic.clinic_email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Globe className="h-3 w-3 mr-1 text-muted-foreground" />
                              <a
                                href={clinic.clinic_website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-indigo-400 hover:underline"
                              >
                                {clinic.clinic_website.replace(
                                  /^https?:\/\//,
                                  '',
                                )}
                              </a>
                            </div>
                            {clinic.human_transfer_destination_number && (
                              <div className="flex items-center text-sm">
                                <Phone className="h-3 w-3 mr-1 text-muted-foreground" />
                                {clinic.human_transfer_destination_number}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {clinic.is_active ? (
                            <Badge variant="success">
                              <Check className="mr-1 h-3 w-3" /> Active
                            </Badge>
                          ) : (
                            <Badge variant="pending">
                              <Clock className="mr-1 h-3 w-3" /> Pending
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(clinic.created_at)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => handleViewOrEdit(clinic._id)}
                              >
                                <Building2 className="mr-2 h-4 w-4" />
                                <span>View Clinic</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleViewOrEdit(clinic._id)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                <span>Edit Details</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleCallAnalytics(clinic._id)}
                              >
                                <Phone className="mr-2 h-4 w-4" />
                                <span>View Call Analytics</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => {
                                  handleDeleteClick(
                                    clinic._id,
                                    clinic.clinic_name,
                                  );
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete Clinic</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {filteredClinics.length > itemsPerPage && (
                  <Pagination className="mt-4">
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            setCurrentPage((p) => Math.max(p - 1, 1))
                          }
                          aria-disabled={currentPage === 1}
                        />
                      </PaginationItem>
                      {Array.from({ length: totalPages }, (_, i) => (
                        <PaginationItem key={i}>
                          <PaginationLink
                            onClick={() => setCurrentPage(i + 1)}
                            isActive={currentPage === i + 1}
                            role="button"
                            tabIndex={0}
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            setCurrentPage((p) => Math.min(p + 1, totalPages))
                          }
                          aria-disabled={currentPage === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <AddClinicDialog
        open={isAddClinicDialogOpen}
        onOpenChange={setIsAddClinicDialogOpen}
        onClinicAdded={refreshClinics}
      />

      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={(open) => {
          if (!isDeleting) {
            setIsDeleteDialogOpen(open);
            if (!open) {
              setClinicToDelete(null);
            }
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Clinic</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;{clinicToDelete?.name}
              &quot;? This action will deactivate the clinic and cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              disabled={isDeleting}
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setClinicToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              disabled={isDeleting}
              onClick={deleteClinic}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Clinic
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
