import Image from 'next/image';
import React from 'react';
import logo from '@/assets/logo-dark.png';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

const notfound = () => {
  return (
    <div className="h-dvh w-full overflow-hidden bg-gradient-to-r from-orange-950/50 to-orange-950/50 via-zinc-950 bg-black flex flex-col items-center justify-center tracking-tighter">
      <Image
        src={logo}
        alt="Logo"
        width={900}
        height={900}
        className="mx-auto object-center object-contain w-40"
      />
      <h1 className="text-5xl font-bold text-white/90 mt-8">Page Not Found</h1>
      <p className="text-lg text-muted-foreground mt-4">
        The page you are looking for does not exist.
      </p>
      <Link href="/dashboard">
        <Button className="mt-8">Dashboard</Button>
      </Link>
    </div>
  );
};

export default notfound;
