'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { fetchAuthSession } from 'aws-amplify/auth';
import { getUserInviteDetails, acceptUserInvite } from '@/actions/user-invites';
import { UserInviteDetails } from '@/lib/types';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';

export default function AcceptInvitePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const token = searchParams.get('token');
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [inviteDetails, setInviteDetails] = useState<UserInviteDetails | null>(
    null,
  );
  const [error, setError] = useState<string | null>(null);
  const [accepted, setAccepted] = useState(false);

  useEffect(() => {
    async function fetchInviteDetails() {
      if (!token) {
        setError('Invalid invitation link. No token provided.');
        setLoading(false);
        return;
      }

      // Always save the token first
      localStorage.setItem('pendingInviteToken', token);
      localStorage.setItem(
        'redirectAfterAuth',
        `/accept-invite?token=${token}`,
      );

      // Wait for auth context to load
      if (authLoading) {
        return;
      }

      // If not authenticated, redirect to login with invitation context
      if (!isAuthenticated) {
        // Ensure token is saved before redirect
        localStorage.setItem('pendingInviteToken', token);
        localStorage.setItem(
          'redirectAfterAuth',
          `/accept-invite?token=${token}`,
        );
        router.push('/login');
        return;
      }

      try {
        const session = await fetchAuthSession({ forceRefresh: true });
        const authToken = session.tokens?.accessToken?.toString();

        if (!authToken) {
          setError('Please log in to view invitation details.');
          setLoading(false);
          return;
        }

        const result = await getUserInviteDetails(token, authToken);
        if (result.ok && result.data) {
          setInviteDetails(result.data);
        } else {
          setError(result.error || 'Failed to fetch invitation details.');
        }
      } catch (err) {
        console.error('Error fetching invite details:', err);
        setError('An unexpected error occurred.');
      } finally {
        setLoading(false);
      }
    }

    fetchInviteDetails();
  }, [token, isAuthenticated, authLoading, router]);

  useEffect(() => {
    return () => {
      if (!accepted && token) {
        const currentPath = window.location.pathname;
        const currentSearch = window.location.search;
        const currentUrl = currentPath + currentSearch;

        if (
          !currentUrl.includes(`/accept-invite?token=${token}`) &&
          !window.location.pathname.includes('/login')
        ) {
          localStorage.removeItem('pendingInviteToken');
          localStorage.removeItem('redirectAfterAuth');
        }
      }
    };
  }, [accepted, token]);

  const handleAcceptInvite = async () => {
    if (!token) return;

    setAccepting(true);

    try {
      const session = await fetchAuthSession({ forceRefresh: true });
      const authToken = session.tokens?.accessToken?.toString();

      if (!authToken) {
        toast.error('Authentication Error', {
          description: 'Please log in again to accept the invitation.',
        });
        setAccepting(false);
        return;
      }
      const result = await acceptUserInvite({ token }, authToken);

      if (result.ok && result.data) {
        toast.success('Invitation Accepted', {
          description: 'You have successfully joined the clinic!',
        });

        // Clear stored invitation tokens after successful acceptance
        localStorage.removeItem('pendingInviteToken');
        localStorage.removeItem('redirectAfterAuth');

        setAccepted(true);
      } else {
        toast.error('Failed to Accept Invitation', {
          description: result.error || 'Please try again later.',
        });
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to Accept Invitation', {
        description: 'An unexpected error occurred. Please try again.',
      });
    } finally {
      setAccepting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading invitation details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              <span>OOPS</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="default">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{error}</AlertTitle>
            </Alert>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            {error.includes('Please log in') ? (
              <>
                <Link href="/login" className="w-full">
                  <Button className="w-full">Log In</Button>
                </Link>
                <Link href="/signup" className="w-full">
                  <Button variant="outline" className="w-full">
                    Sign Up
                  </Button>
                </Link>
              </>
            ) : null}
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (accepted) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Check className="h-5 w-5 text-green-500" />
              <span>Invitation Accepted</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="bg-green-400/10 border-green-200/70">
              <AlertTitle className="dark:text-green-400">Welcome!</AlertTitle>
              <AlertDescription className="dark:text-green-400">
                You have successfully joined the clinic. You can now access your
                dashboard.
              </AlertDescription>
            </Alert>
            <div className="mt-4">
              <Button
                onClick={() => (window.location.href = '/dashboard')}
                className="w-full"
              >
                Go to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Clinic Invitation</CardTitle>
          <CardDescription>
            You have been invited to join a clinic
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {inviteDetails && (
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Invited by
                </p>
                <p className="font-medium">{inviteDetails.data.inviter_name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Role
                </p>
                <p className="font-medium capitalize">
                  {inviteDetails.data.role}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Invited as
                </p>
                <p className="font-medium">{inviteDetails.data.invitee_name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Email
                </p>
                <p className="font-medium">
                  {inviteDetails.data.invitee_email}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Status
                </p>
                <p className="font-medium capitalize">
                  {inviteDetails.data.accepted ? 'accepted' : 'pending'}
                </p>
              </div>
            </div>
          )}

          <div className="pt-4">
            <Button
              onClick={handleAcceptInvite}
              disabled={accepting || inviteDetails?.data.accepted}
              className="w-full"
            >
              {accepting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Accepting...
                </>
              ) : (
                'Accept Invitation'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
