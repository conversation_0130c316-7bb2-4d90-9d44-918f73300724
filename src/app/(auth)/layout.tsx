'use client';

import Image from 'next/image';
import robot from '@/assets/login.png';
import logo from '@/assets/logo-dark.png';
import logo2 from '@/assets/logo-light.png';
import { useTheme } from 'next-themes';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { theme, systemTheme } = useTheme();
  const currentTheme = theme === 'system' ? systemTheme : theme;
  const logoSrc = currentTheme !== 'dark' ? logo : logo2;

  return (
    <div className="min-h-dvh items-center grid md:grid-cols-2 grid-cols-1">
      <div className="absolute top-10 left-20 text-xl font-bold w-32">
        <Image
          src={logoSrc}
          alt="logo"
          placeholder="blur"
          width={200}
          height={100}
        />
      </div>
      {children}
      <div className="h-dvh w-full hidden md:block overflow-hidden">
        <Image
          src={robot}
          placeholder="blur"
          alt="Logo"
          width={600}
          height={900}
          className="mx-auto object-center object-contain bg-background size-full"
        />
      </div>
    </div>
  );
}
