'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Link from 'next/link';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import {
  confirmSignUp,
  signUp,
  autoSignIn,
  fetchAuthSession,
} from 'aws-amplify/auth';
import { createUser } from '@/services/userCreationService';
import '../../../lib/amplify';
import { useAuth } from '@/contexts/AuthContext';
import { getUserClinics } from '@/services/clinicService';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
});

export default function SignupPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);
  const router = useRouter();
  const auth = useAuth();

  // Check if user is coming from an invitation
  const hasPendingInvite =
    typeof window !== 'undefined' &&
    (localStorage.getItem('pendingInviteToken') ||
      localStorage.getItem('redirectAfterAuth'));

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    try {
      const { nextStep } = await signUp({
        username: values.email,
        options: {
          userAttributes: {
            email: values.email,
            given_name: values.firstName,
            family_name: values.lastName,
          },
          autoSignIn: {
            authFlowType: 'USER_AUTH',
          },
        },
      });

      if (nextStep.signUpStep === 'CONFIRM_SIGN_UP') {
        setShowOtpVerification(true);
        toast.success('Verification Code Sent', {
          description: 'Please check your email for the verification code',
        });
      } else {
        toast.error('Unexpected Step', {
          description: nextStep.signUpStep,
        });
      }
    } catch (error) {
      if ((error as Error).name === 'UsernameExistsException') {
        setTimeout(() => {
          router.push('/login');
        }, 1500);

        toast.error('Account Already Exists', {
          description: 'Please log in to your existing account.',
        });
      } else {
        toast.error('Sign Up Failed', {
          description: 'Something went wrong. Please try again.',
        });
      }
    } finally {
      setIsLoading(false);
    }
  }

  const onOtpSubmit = async (otp: string) => {
    const email = form.getValues().email;
    try {
      const { nextStep } = await confirmSignUp({
        username: email,
        confirmationCode: otp,
      });

      if (nextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        const signInResponse = await autoSignIn();
        if (signInResponse.isSignedIn) {
          await onOtpVerified();
        } else {
          toast.error('Auto Sign-In Failed', {
            description:
              'Could not automatically sign you in. Please try logging in.',
          });
          router.push('/login');
        }
      } else if (nextStep.signUpStep === 'DONE') {
        await onOtpVerified();
      } else {
        toast.error('Unexpected Sign-Up Step', {
          description: `After OTP, got step: ${nextStep.signUpStep}. Please try logging in.`,
        });
        router.push('/login');
      }
    } catch (error) {
      console.error('Error confirming sign-up:', error);
      toast.error('Verification Failed', {
        description: 'Invalid verification code. Please try again.',
      });
      setShowOtpVerification(false);
      form.reset();
    }
  };

  const onOtpVerified = async () => {
    setIsCreatingProfile(true);

    try {
      console.log(
        'onOtpVerified (Signup) - Calling auth.checkAuth() to refresh context',
      );
      const userDetails = await auth.checkAuth();

      if (!userDetails) {
        throw new Error(
          'Session could not be established after sign-up. Please try logging in.',
        );
      }

      const session = await fetchAuthSession({ forceRefresh: true });
      const accessToken = session.tokens?.accessToken?.toString();
      const idToken = session.tokens?.idToken?.toString();

      if (!accessToken || !idToken) {
        throw new Error(
          'Failed to retrieve authentication tokens after sign-up.',
        );
      }

      const formValues = form.getValues();
      const userCreationResult = await createUser(
        {
          first_name: formValues.firstName,
          last_name: formValues.lastName,
        },
        accessToken,
        idToken,
      );

      if (!userCreationResult.ok) {
        throw new Error(
          userCreationResult.error ||
            'Failed to create user profile in backend.',
        );
      }

      toast.success('Account Created Successfully', {
        description: 'Welcome to Smart Reception!',
      });

      // Check if there's a pending invitation or redirect
      const redirectAfterAuth = localStorage.getItem('redirectAfterAuth');
      const pendingInviteToken = localStorage.getItem('pendingInviteToken');

      if (redirectAfterAuth) {
        // Clear the redirect flag and go to the stored URL
        localStorage.removeItem('redirectAfterAuth');
        router.push(redirectAfterAuth);
        return;
      }

      if (pendingInviteToken) {
        // Clear the token and redirect to accept-invite
        localStorage.removeItem('pendingInviteToken');
        router.push(`/accept-invite?token=${pendingInviteToken}`);
        return;
      }

      // Check if user has clinics after account creation
      try {
        const clinicsResult = await getUserClinics(accessToken);

        if (
          clinicsResult.ok &&
          clinicsResult.data &&
          clinicsResult.data.length > 0
        ) {
          // User has clinics, redirect to dashboard
          router.push('/dashboard');
        } else {
          // User has no clinics, redirect to onboarding
          router.push('/onboarding');
        }
      } catch (clinicError) {
        console.error('Error checking user clinics:', clinicError);
        // Fallback to onboarding for new users
        router.push('/onboarding');
      }
    } catch (error) {
      console.error('Error during onOtpVerified (Signup):', error);
      toast.error('Profile Creation or Finalization Failed', {
        description:
          (error as Error).message ||
          "Your account was created, but we couldn't finalize your profile. Please try logging in or contact support.",
      });
      if (auth.isAuthenticated) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    } finally {
      setIsCreatingProfile(false);
    }
  };

  if (isCreatingProfile) {
    return (
      <div className="min-h-dvh flex items-center justify-center">
        <Card className="w-full max-w-sm py-10 mx-auto">
          <CardContent className="flex flex-col items-center gap-4">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-2 border-primary"></div>
            <p className="text-center text-muted-foreground">
              Creating your profile...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow="SIGNUP"
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <div className="w-full mx-auto">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold">
                Create Account
              </CardTitle>
              <CardDescription>
                {hasPendingInvite
                  ? 'Create your account to accept the clinic invitation'
                  : 'Enter your details to create your Smart Reception account'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input placeholder="John" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </Button>
                </form>
              </Form>
              <div className="mt-4 text-center text-sm">
                Already have an account?{' '}
                <Link href="/login" className="text-primary hover:underline">
                  Login here
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
