'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { fetchAuthSession } from 'aws-amplify/auth';
import { createClinic } from '@/services/clinicService';
import { CheckCircle, ArrowRight, Building2, Loader2 } from 'lucide-react';

// Form schemas for validation
const step1Schema = z.object({
  clinic_name: z.string().min(1, { message: 'Clinic name is required' }),
  clinic_email: z
    .string()
    .email({ message: 'Please enter a valid email address' }),
  clinic_phone: z.string().min(1, { message: 'Phone number is required' }),
  clinic_website: z
    .string()
    .regex(
      /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid website URL',
    ),
});

const step2Schema = z.object({
  // Optional fields for step 2 - can be added later
});

type Step1FormData = z.infer<typeof step1Schema>;
type Step2FormData = z.infer<typeof step2Schema>;

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const step1Form = useForm<Step1FormData>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      clinic_name: '',
      clinic_email: '',
      clinic_phone: '',
      clinic_website: '',
    },
  });

  const step2Form = useForm<Step2FormData>({
    resolver: zodResolver(step2Schema),
    defaultValues: {},
  });

  const steps = [
    {
      id: 1,
      title: 'Essential Information',
      description: "Let's start with the basic details about your clinic",
      icon: Building2,
    },
    {
      id: 2,
      // title: 'Additional Details',
      // description: 'Optional information to enhance your clinic profile',
      // icon: Users,
    },
  ];

  const handleStep1Submit = async () => {
    setCurrentStep(2);
  };

  const handleStep2Submit = async () => {
    await submitClinicData();
  };

  // const handleSkipStep2 = async () => {
  //   await submitClinicData();
  // };

  const submitClinicData = async () => {
    setIsSubmitting(true);

    try {
      // Get the auth session to retrieve tokens
      const session = await fetchAuthSession({ forceRefresh: true });
      const accessToken = session.tokens?.accessToken?.toString();

      if (!accessToken) {
        throw new Error('Failed to retrieve authentication token');
      }

      const step1Data = step1Form.getValues();

      const clinicCreationResult = await createClinic(
        {
          clinic_name: step1Data.clinic_name,
          clinic_email: step1Data.clinic_email,
          clinic_phone: step1Data.clinic_phone,
          clinic_website: step1Data.clinic_website,
        },
        accessToken,
      );

      if (!clinicCreationResult.ok) {
        throw new Error(
          clinicCreationResult.error || 'Failed to create clinic',
        );
      }

      toast.success('Clinic Created Successfully', {
        description: 'Your clinic profile has been set up!',
      });

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Error creating clinic:', error);
      toast.error('Clinic Creation Failed', {
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps.find((step) => step.id === currentStep);
  const Icon = currentStepData?.icon || Building2;

  if (isSubmitting) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-sm py-10 mx-auto">
          <CardContent className="flex flex-col items-center gap-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-center text-muted-foreground">
              Creating your clinic profile...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <div className="text-2xl font-bold mb-2">
            <span className="text-primary">Smart</span>
            Reception
          </div>
          <div className="flex justify-center gap-2 mb-4">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`h-2 w-8 rounded-full transition-colors ${
                  step.id <= currentStep ? 'bg-primary' : 'bg-muted'
                }`}
              />
            ))}
          </div>
        </div>

        <Card className="w-full">
          <CardHeader className="text-center">
            {currentStep === 1 ? (
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <Icon className="h-6 w-6 text-primary" />
              </div>
            ) : null}
            <CardTitle className="text-2xl">{currentStepData?.title}</CardTitle>
            <CardDescription className="text-base">
              {currentStepData?.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {currentStep === 1 && (
              <Form {...step1Form}>
                <form
                  onSubmit={step1Form.handleSubmit(handleStep1Submit)}
                  className="space-y-4"
                >
                  <FormField
                    control={step1Form.control}
                    name="clinic_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="City Health Clinic" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={step1Form.control}
                    name="clinic_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic Email *</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={step1Form.control}
                    name="clinic_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic Phone *</FormLabel>
                        <FormControl>
                          <Input placeholder="+****************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={step1Form.control}
                    name="clinic_website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Clinic Website *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://www.clinic.com"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="mt-4">
                    Continue
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </form>
              </Form>
            )}

            {currentStep === 2 && (
              <Form {...step2Form}>
                <form
                  onSubmit={step2Form.handleSubmit(handleStep2Submit)}
                  className="space-y-4"
                >
                  <div className="text-center py-8">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Almost Done!</h3>
                    <p className="text-muted-foreground">
                      You can add more details later in your clinic profile
                      settings.
                    </p>
                  </div>
                  <div className="flex gap-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    {/* <Button
                      type="button"
                      variant="outline"
                      onClick={handleSkipStep2}
                      className="flex-1"
                    >
                      Skip
                    </Button> */}
                    <Button type="submit" className="flex-1">
                      Complete Setup
                      <CheckCircle className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
