'use client';

import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { createSupportRequest } from '@/actions/help-support';
import { useAuth } from '@/contexts/AuthContext';

const formSchema = z.object({
  subject: z
    .string()
    .trim()
    .min(3, 'Subject must be at least 3 characters')
    .max(100, 'Subject is too long'),
  message: z
    .string()
    .trim()
    .min(5, 'Message must be at least 5 characters')
    .max(1000, 'Message is too long'),
});

type FormValues = z.infer<typeof formSchema>;

export default function HelpSupportPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: '',
      message: '',
    },
  });

  const onSubmit = useCallback(
    async (values: FormValues) => {
      try {
        setIsSubmitting(true);
        const { accessToken } = await getTokens();

        if (!accessToken) {
          throw new Error('Authentication required');
        }

        await createSupportRequest(accessToken, values);
        toast.success('Your message has been sent successfully!');
        form.reset();
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to send message. Please try again.';
        toast.error(errorMessage);
      } finally {
        setIsSubmitting(false);
      }
    },
    [getTokens, form],
  );

  return (
    <div className="container max-w-3xl mx-auto py-8 px-4">
      <Card>
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold">Help & Support</CardTitle>
          <p className="text-muted-foreground">
            Fill out the form below and our team will get back to you as soon as
            possible.
          </p>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subject</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="What's this regarding?"
                        {...field}
                        disabled={isSubmitting}
                        autoComplete="off"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please describe your issue in detail..."
                        className="min-h-[150px] resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end pt-2">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
