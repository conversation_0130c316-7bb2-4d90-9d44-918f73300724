'use client';

import { AlertCircle } from 'lucide-react';

import { useClinic } from '@/contexts/ClinicContext';
import { ClinicProfileForm } from '@/components/clinic';
import { toast } from 'sonner';
import { updateClinic } from '@/services/clinicService';
import { useAuth } from '@/contexts/AuthContext';
import { ClinicFormValues } from '@/lib/clinic-form-schema';

export default function ClinicProfilePage() {
  const { selectedClinic, setSelectedClinic } = useClinic();
  const { getTokens } = useAuth();

  // Removed clinicChanged event listener to prevent recursion
  // The context already updates selectedClinic directly when setSelectedClinic is called

  if (!selectedClinic) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No clinic selected</p>
        </div>
      </div>
    );
  }

  const handleClinicFormSubmit = async (values: ClinicFormValues) => {
    try {
      const { accessToken } = await getTokens();

      if (!accessToken || !selectedClinic) {
        toast.error('Authentication failed');
        return;
      }

      const result = await updateClinic(
        selectedClinic._id,
        values,
        accessToken,
      );

      if (result.ok && result.data) {
        toast.success('Clinic updated successfully');
        setSelectedClinic(result.data);
      } else {
        toast.error(result.error || 'Update failed');
      }
    } catch (err) {
      console.error(err);
      toast.error('An unexpected error occurred');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Clinic Profile</h1>
        <p className="text-muted-foreground">
          Manage your clinic information and settings
        </p>
      </div>

      <ClinicProfileForm
        selectedClinic={selectedClinic}
        onSubmitHandler={handleClinicFormSubmit}
      />
    </div>
  );
}
