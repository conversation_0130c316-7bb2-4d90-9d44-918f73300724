export interface UserDetails {
  uid: string;
  email: string;
  name?: string;
  role?: string;
  isAdmin: boolean;
}

/**
 * Parses a JWT token without verification
 * @param token JWT token string
 * @returns Decoded token payload or null if invalid
 */
interface JwtPayload {
  sub?: string;
  email?: string;
  name?: string;
  'cognito:username'?: string;
  'cognito:groups'?: string[];
  [key: string]: unknown;
}

export function parseJwt(token: string): JwtPayload | null {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join(''),
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
}

/**
 * Extracts user details from the ID token
 * @param idToken ID token from Cognito
 * @returns User details object
 */
export function getUserFromToken(idToken: string): UserDetails | null {
  try {
    const decoded = parseJwt(idToken);
    if (!decoded) return null;

    // Use sub as uid since it's the unique identifier in Cognito
    const uid = decoded.sub || '';
    const email = decoded.email || '';
    const name = decoded.name || decoded['cognito:username'] || '';
    const isAdmin = decoded['cognito:groups']?.includes('admin') || false;

    return {
      uid,
      email,
      name,
      isAdmin,
      // Add any other user attributes you need from the token
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * Gets the current user from localStorage
 * @returns User details or null if not authenticated
 */
export function getCurrentUserFromStorage(): UserDetails | null {
  if (typeof window === 'undefined') return null;

  try {
    // Find the ID token in localStorage
    const tokenKey = Object.keys(localStorage).find(
      (key) =>
        key.includes('CognitoIdentityServiceProvider') &&
        key.includes('idToken'),
    );

    if (!tokenKey) return null;

    const idToken = localStorage.getItem(tokenKey);
    if (!idToken) return null;

    return getUserFromToken(idToken);
  } catch (error) {
    console.error('Error getting current user from storage:', error);
    return null;
  }
}
