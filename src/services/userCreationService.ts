'use client';

import { API_ENDPOINTS } from '@/actions';

export interface UserCreationData {
  first_name: string;
  last_name: string;
}

export interface UserCreationResponse {
  data: {
    first_name: string;
    last_name: string;
    email: string;
    uid: string;
    _id: string;
    created_at: string;
    updated_at: string;
    __v: number;
  };
}

export interface UserCreationRequest {
  user_data: UserCreationData;
}

/**
 * Creates a new user in the backend after successful Cognito signup
 * @param userData User data containing first_name and last_name
 * @param accessToken Cognito access token
 * @param idToken Cognito ID token
 * @returns Promise with the user creation response
 */
export async function createUser(
  userData: UserCreationData,
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: UserCreationResponse;
  error?: string;
}> {
  try {
    const requestBody: UserCreationRequest = {
      user_data: userData,
    };

    const response = await fetch(API_ENDPOINTS.USERS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to create user profile',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data,
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while creating user profile',
    };
  }
}
