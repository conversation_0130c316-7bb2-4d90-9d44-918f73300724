'use client';

import { API_ENDPOINTS } from '@/actions';
import { getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';
import { StaffMember, StaffMembersResponse } from '@/lib/types';

export interface UserDetails {
  uid: string;
  email: string;
  role?: string;
  isAdmin: boolean;
}

export interface UserProfile {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  uid: string;
  created_at: string;
  updated_at: string;
  __v: number;
}

export interface UserProfileResponse {
  data: UserProfile;
}

/**
 * Fetches the current authenticated user details from AWS Cognito
 */
export async function getAuthenticatedUser(): Promise<UserDetails | null> {
  try {
    // Get current user from Amplify
    const currentUser = await getCurrentUser();
    const userAttributes = await fetchUserAttributes();

    return {
      uid: currentUser.userId,
      email: userAttributes.email || '',
      isAdmin: false, // Default value, will be updated by getUserDetails
    };
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

/**
 * Fetches user details including role from the backend API
 * @param token Access token for authorization
 */
export async function getUserDetails(
  token: string,
): Promise<UserDetails | null> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_PROFILE, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user details: ${response.status}`);
    }

    const data = await response.json();

    // Extract user details from response
    const userDetails: UserDetails = {
      uid: data.uid || '',
      email: data.email || '',
      role: data.role || '',
      isAdmin: data.role === 'admin',
    };

    return userDetails;
  } catch (error) {
    console.error('Error fetching user details:', error);
    return null;
  }
}

/**
 * Fetches staff members for a specific clinic
 * @param clinicId The clinic ID to fetch staff for
 * @param token Access token for authorization
 * @returns Promise<StaffMember[]> Array of staff members
 */
export async function getStaffMembers(
  clinicId: string,
  token: string,
): Promise<StaffMember[]> {
  try {
    const response = await fetch(
      `${API_ENDPOINTS.CLINIC_OWNERS}?clinic_id=${clinicId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch staff members: ${response.statusText}`);
    }

    const result: StaffMembersResponse = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching staff members:', error);
    throw error;
  }
}

/**
 * Changes the role of a user in a clinic
 * @param userId The user ID whose role is being changed
 * @param clinicId The clinic ID
 * @param role The new role to assign
 * @param token Access token for authorization
 * @returns Promise with the operation result
 */
export async function changeUserRole(
  userId: string,
  clinicId: string,
  role: string,
  token: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_CLINIC_ROLES, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-id-token': idToken,
      },
      body: JSON.stringify({
        user_id: userId,
        clinic_id: clinicId,
        role: role,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      const errorMessage =
        data?.error?.message || data?.message || 'Failed to update role';
      return {
        ok: false,
        status: response.status,
        error: errorMessage,
      };
    }

    return {
      ok: true,
      status: response.status,
    };
  } catch (error) {
    console.error('Error changing user role:', error);
    return {
      ok: false,
      status: 500,
      error: error instanceof Error ? error.message : 'Failed to update role',
    };
  }
}

/**
 * Removes a user from a clinic
 * @param userId The user ID to remove
 * @param clinicId The clinic ID
 * @param token Access token for authorization
 * @returns Promise with the operation result
 */
export async function removeUserFromClinic(
  userId: string,
  clinicId: string,
  token: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  error?: string;
}> {
  try {
    const response = await fetch(
      `${API_ENDPOINTS.USER_CLINIC_ROLES}?user_id=${userId}&clinic_id=${clinicId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          'x-id-token': idToken,
        },
      },
    );

    const data = await response.json();

    if (!response.ok) {
      const errorMessage =
        data?.error?.message ||
        data?.message ||
        'Failed to remove staff member';
      return {
        ok: false,
        status: response.status,
        error: errorMessage,
      };
    }

    return {
      ok: true,
      status: response.status,
    };
  } catch (error) {
    console.error('Error removing user from clinic:', error);
    return {
      ok: false,
      status: 500,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to remove staff member',
    };
  }
}
