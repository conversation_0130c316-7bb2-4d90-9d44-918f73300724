'use client';

import { API_ENDPOINTS } from '.';
import type { IListCallsApiData } from '@/lib/types';

interface ApiResponse {
  data: IListCallsApiData;
  error?: string;
}

/**
 * Fetches analytics data from the backend API
 * @param token Access token for authorization
 * @param clinicId ID of the clinic to fetch analytics for
 * @param startDate Start date in YYYY-MM-DD format
 * @param endDate End date in YYYY-MM-DD format
 * @returns Promise with the analytics data response
 */
export async function fetchAnalyticsData(
  token: string,
  clinicId: string,
  startDate: string,
  endDate: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: IListCallsApiData;
  error?: string;
}> {
  try {
    if (!token) {
      return {
        ok: false,
        status: 401,
        error: 'No access token provided',
      };
    }

    const url = `${API_ENDPOINTS.DB_ANALYTICS_LIST_CALLS}?clinic_id=${clinicId}&start_date=${startDate}&end_date=${endDate}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 60,
        tags: ['analytics-data'],
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to fetch analytics data',
      };
    }

    const result: ApiResponse = await response.json();
    return {
      ok: true,
      status: response.status,
      data: result.data,
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching analytics data',
    };
  }
}
