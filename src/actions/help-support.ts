import { API_ENDPOINTS } from '.';

export async function createSupportRequest(
  token: string,
  data: {
    subject: string;
    message: string;
  },
) {
  try {
    const url = new URL(API_ENDPOINTS.USER_SUPPORT_REQUEST);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to create support request. Please try again.',
    };
  }
}
