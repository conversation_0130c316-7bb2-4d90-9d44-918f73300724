# Check out https://hub.docker.com/_/node to select a new base image
FROM --platform=linux/amd64 node:22-alpine

# create and set app directory
ARG CODE_SOURCE=/home/<USER>/app
RUN mkdir -p $CODE_SOURCE
WORKDIR $CODE_SOURCE

# Bundle app source
COPY . $CODE_SOURCE

# Next.js embeds NEXT_PUBLIC_* env variables into the static frontend bundle at build time, not runtime
ENV NEXT_PUBLIC_COGNITO_USER_POOL_ID=ap-southeast-2_lYVonMyEL 
ENV NEXT_PUBLIC_COGNITO_USER_POOL_CLIENT_ID=50dsf5l0ase8fr380gq4drpksp

# Build this app
RUN yarn install
RUN yarn run build

# Bind to all network interfaces so that it can be mapped to the host OS
ENV HOST=0.0.0.0 PORT=3000 

EXPOSE ${PORT}
CMD [ "yarn", "run", "start" ]