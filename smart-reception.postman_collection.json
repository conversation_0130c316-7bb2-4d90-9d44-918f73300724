{"info": {"_postman_id": "c2f3a128-ecea-4f6d-8991-dafedacd8892", "name": "smart-reception", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "3889324", "_collection_link": "https://interstellar-meadow-61687.postman.co/workspace/IndMoney~d6c4ea6f-69b8-4a14-872a-fe52931155ec/collection/3889324-c2f3a128-ecea-4f6d-8991-dafedacd8892?action=share&source=collection_link&creator=3889324"}, "item": [{"name": "Retell /function-call", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_952adaf8c1ecb08e382362765b\"\n    },\n    \"name\": \"get_available_appointment_slots\",\n    \"args\": {\n  \"from_date\": \"2023-10-05\",\n  \"to_date\": \"2023-10-10\",\n  \"practitioner_id\": \"1691801777998931607\"\n}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/agent/function-call", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "agent", "function-call"]}}, "response": [{"name": "book_appointment_for_new_patient", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"call_id\": \"call_e7165bb72ab76d75eb9d2310eda\",\n        \"call_type\": \"phone_call\",\n        \"agent_id\": \"agent_508557a3dd8c20b18836a4dcba\",\n        \"call_status\": \"ongoing\",\n        \"start_timestamp\": 1744295450816,\n        \"transcript\": \"Agent: Hi, this is <PERSON><PERSON> from \\nAgent: Smart \\nUser: This call is now being recorded.\\nAgent: Thank you for letting me \\nUser: Hello?\\nAgent: know! How can I assist you today? Would you like to book an appointment or need some information?\\nUser: Uh, who are you?\\nAgent: Hi there! I'm <PERSON><PERSON>, the receptionist at Smart Reception Clinic. How can I assist you today?\\nUser: I was looking to book an appointment.\\nAgent: Great! Are you a new patient or an existing patient with us?\\nUser: I am a new person.\\nAgent: Thank you for that! I’ll need to collect some details to get you booked in. Could you please provide me with your full name?\\nUser: Uh, my full name is <PERSON><PERSON><PERSON>.\\nAgent: Thank you, <PERSON><PERSON><PERSON>. Could you please provide me with your mobile number?\\nUser: It's seven two zero six four seven\\nUser: nine eight double four.\\nAgent: Just to confirm, your mobile number is seven two zero, six four seven, nine eight, double four. Is that correct?\\nUser: Correct. Yeah. Yeah. Correct.\\nAgent: Thank you for confirming! Now, could you please provide your date of birth in the format of year, month, and day?\\nUser: It's nine January ninety six.\\nAgent: Thank you! Just to confirm, your date of birth is the ninth of January, nineteen ninety-six. Is that correct?\\nUser: Correct.\\nAgent: Great! Lastly, could you please provide your address?\\nUser: I am from Gujarat, India.\\nAgent: Thank you, Varun. Just to clarify, could you please provide a more specific address, including any street name or number, if possible? This helps us with your appointment details.\\nUser: No. Uh, just write Gujarat, India.\\nAgent: Understood! I’ll note your address as Gujarat, India.Now, could you please let me know the reason for your visit? This will help us schedule the appropriate appointment for you.\\nUser: Yeah. It's just general consultation with the doctor.\\nAgent: Thank you for that information! Now, let’s find a suitable appointment for you. How does this Thursday at 10 AM sound?\\nUser: Yeah. Cool. Can you book it?\\nAgent: Just to confirm, you would like to book a general consultation appointment for you, Varun Kumar, on Thursday at 10 AM, with the following details:\\n\\n- Mobile Number: seven two zero, six four seven, nine eight, double four\\n- Date of Birth: ninth of January, nineteen ninety-six\\n- Address: Gujarat, India\\n\\nIs everything correct?\\nUser: Correct. Yes. Please go ahead.\\n\",\n        \n        \"public_log_url\": \"https://dxc03zgurdly9.cloudfront.net/a07527db6c3e1906a222de4a00bb2b93b9a1367a3034ec766352fae6b940871e/public.log\",\n        \"latency\": {},\n        \"call_cost\": {\n            \"total_duration_unit_price\": 0,\n            \"product_costs\": [],\n            \"total_one_time_price\": 0,\n            \"combined_cost\": 0,\n            \"total_duration_seconds\": 0\n        },\n        \"opt_out_sensitive_data_storage\": false,\n        \"from_number\": \"+************\",\n        \"to_number\": \"+***********\",\n        \"direction\": \"inbound\",\n        \"telephony_identifier\": {\n            \"twilio_call_sid\": \"CAd41a92b4b18d1a6760a9703ea4365a5d\"\n        }\n    },\n    \"name\": \"book_appointment_for_new_patient\",\n    \"args\": {\n        \"patient_mobile\": \"**********\",\n        \"patient_first_name\": \"testF\",\n        \"patient_last_name\": \"testL\",\n        \"patient_dob\": \"1996-01-09\",\n        \"practitioner_id\": \"1664574157834167650\",\n        \"appointment_date\": \"2025-10-05T10:00:00+10:00\",\n        \"reason_for_visit\": \"general consultation\",\n        \"patient_address\": \"Gujarat, India\"\n\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/api/agent/function-call", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "3443"}, {"key": "ETag", "value": "W/\"d73-GWL290fszlYEP5xSq+LNN5VrPPA\""}, {"key": "Date", "value": "Thu, 17 Apr 2025 04:06:22 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"success\": true,\n        \"patient\": {\n            \"accepted_email_marketing\": false,\n            \"accepted_privacy_policy\": null,\n            \"accepted_sms_marketing\": false,\n            \"address_1\": \"Gujarat, India\",\n            \"address_2\": null,\n            \"address_3\": null,\n            \"appointment_notes\": null,\n            \"archived_at\": null,\n            \"city\": null,\n            \"country\": \"India\",\n            \"country_code\": \"IN\",\n            \"created_at\": \"2025-04-17T04:06:21Z\",\n            \"custom_fields\": null,\n            \"date_of_birth\": \"1996-01-09\",\n            \"deleted_at\": null,\n            \"dva_card_number\": null,\n            \"email\": null,\n            \"emergency_contact\": null,\n            \"first_name\": \"testF\",\n            \"gender\": null,\n            \"gender_identity\": null,\n            \"id\": \"1665658630583625287\",\n            \"invoice_default_to\": null,\n            \"invoice_email\": null,\n            \"invoice_extra_information\": null,\n            \"label\": \"testF testL\",\n            \"last_name\": \"testL\",\n            \"medicare\": null,\n            \"medicare_reference_number\": null,\n            \"merged_at\": null,\n            \"notes\": null,\n            \"occupation\": null,\n            \"old_reference_id\": null,\n            \"patient_phone_numbers\": [\n                {\n                    \"normalized_number\": \"************\",\n                    \"number\": \"**********\",\n                    \"phone_type\": \"Mobile\"\n                }\n            ],\n            \"post_code\": null,\n            \"preferred_first_name\": null,\n            \"pronouns\": null,\n            \"receives_cancellation_emails\": true,\n            \"receives_confirmation_emails\": true,\n            \"referral_source\": null,\n            \"reminder_type\": \"SMS & Email\",\n            \"sex\": null,\n            \"state\": null,\n            \"time_zone\": null,\n            \"title\": null,\n            \"updated_at\": \"2025-04-17T04:06:21Z\",\n            \"appointments\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287/appointments\"\n                }\n            },\n            \"attendees\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/attendees?q%5B%5D=patient_id%3A%3D1665658630583625287\"\n                }\n            },\n            \"invoices\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287/invoices\"\n                }\n            },\n            \"patient_attachments\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287/patient_attachments\"\n                }\n            },\n            \"medical_alerts\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287/medical_alerts\"\n                }\n            },\n            \"relationships\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/relationships?q%5B%5D=patient_id%3A%3D1665658630583625287\"\n                }\n            },\n            \"links\": {\n                \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287\"\n            }\n        },\n        \"appointment\": {\n            \"archived_at\": null,\n            \"booking_ip_address\": null,\n            \"cancellation_note\": null,\n            \"cancellation_reason\": null,\n            \"cancellation_reason_description\": \"\",\n            \"cancelled_at\": null,\n            \"conflicts\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/individual_appointments/1665658637252569031/conflicts\"\n                }\n            },\n            \"created_at\": \"2025-04-17T04:06:22Z\",\n            \"deleted_at\": null,\n            \"did_not_arrive\": false,\n            \"email_reminder_sent\": false,\n            \"ends_at\": \"2025-10-05T00:45:00Z\",\n            \"has_patient_appointment_notes\": false,\n            \"id\": \"1665658637252569031\",\n            \"invoice_status\": null,\n            \"notes\": \"general consultation\",\n            \"online_booking_policy_accepted\": null,\n            \"patient_arrived\": false,\n            \"patient_name\": \"testF testL\",\n            \"repeat_rule\": {},\n            \"repeats\": null,\n            \"sms_reminder_sent\": false,\n            \"starts_at\": \"2025-10-05T00:00:00Z\",\n            \"telehealth_url\": \"https://bannerbot.au4.cliniko.com/appointments/1665658637252569031/connect\",\n            \"treatment_note_status\": null,\n            \"updated_at\": \"2025-04-17T04:06:22Z\",\n            \"appointment_type\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/appointment_types/1664574160954729989\"\n                }\n            },\n            \"business\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/businesses/1664574161281885744\"\n                }\n            },\n            \"practitioner\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1664574157834167650\"\n                }\n            },\n            \"patient\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1665658630583625287\"\n                }\n            },\n            \"attendees\": {\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/individual_appointments/1665658637252569031/attendees\"\n                }\n            },\n            \"links\": {\n                \"self\": \"https://api.au4.cliniko.com/v1/individual_appointments/1665658637252569031\"\n            }\n        }\n    }\n}"}, {"name": "get_practitioners_list", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_aad9ed15cc0825a8113bf2a247\"\n    },\n    \"name\": \"get_practitioners_list\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/agent/function-call", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 10:56:39 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"43d-nCE1rOay17xHsokbYo54kX5c0uY\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=GlhzsD%2F%2FNUnWGj%2FO8Tf9cnjpWHG8%2FAIBaVQ1Aubtko%2BNwFfVYibhWYArhm732NvXbc4dYKeZJOLvUX7tejUX9GERSgqYn%2BZuL%2FfBZMP%2Fk3EvJo5iCW38rvBKgYfOrLL0%2BVrTaffALLM%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f1a160caa12cb1-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"practitioners\": [\n            {\n                \"active\": true,\n                \"created_at\": \"2025-05-12T15:03:33Z\",\n                \"description\": \"\",\n                \"designation\": \"Neurologist\",\n                \"display_name\": \"Varun\",\n                \"first_name\": \"Varun\",\n                \"id\": \"1684108802892244198\",\n                \"label\": \"<PERSON>aru<PERSON>\",\n                \"last_name\": \"<PERSON>\",\n                \"show_in_online_bookings\": true,\n                \"title\": null,\n                \"updated_at\": \"2025-05-12T15:13:54Z\",\n                \"user\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/users/1684108802716083845\"\n                    }\n                },\n                \"appointments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1684108802892244198/appointments\"\n                    }\n                },\n                \"appointment_types\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1684108802892244198/appointment_types\"\n                    }\n                },\n                \"invoices\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1684108802892244198/invoices\"\n                    }\n                },\n                \"practitioner_reference_numbers\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1684108802892244198/practitioner_reference_numbers\"\n                    }\n                },\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/practitioners/1684108802892244198\"\n                }\n            }\n        ],\n        \"total_entries\": 1,\n        \"links\": {\n            \"self\": \"https://api.au4.cliniko.com/v1/practitioners?page=1\"\n        }\n    }\n}"}, {"name": "get_available_appointment_slots", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_aad9ed15cc0825a8113bf2a247\"\n    },\n    \"name\": \"get_available_appointment_slots\",\n    \"args\": {\n        \"practitioner_id\": \"1684108802892244198\",\n        \"from_date\": \"2025-05-15\",\n        \"to_date\": \"2025-05-15\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/agent/function-call", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 11:05:56 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"2ac-vsDfWVraQkN1/s2A+CrNJJR9OSQ\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=KEz1Fna5tNljaBshiEcc1lE44YVLnFJVKqEOdnSMgKk2%2BNdcvA3BOTQKaVNg4NL%2FZBNsS7AehACB9K0C%2F1l3SjNHZl1ltUXwwWUAbhKdeAGC0qqZw%2F%2F3mwDymcLsbtFCYtT81JJWI%2B0%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f1aefbeca880c2-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"slots\": {\n            \"available_times\": [\n                {\n                    \"appointment_start\": \"2025-05-15T03:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T04:15:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T05:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T05:45:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T06:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T07:15:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T08:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T08:45:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-15T09:30:00Z\"\n                }\n            ],\n            \"total_entries\": 9,\n            \"links\": {\n                \"self\": \"https://api.au4.cliniko.com/v1/businesses/1684108806834889941/practitioners/1684108802892244198/appointment_types/1684108806004417595/available_times?from=2025-05-15&to=2025-05-15&page=1\"\n            }\n        }\n    }\n}"}, {"name": "local get slots", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_837085a4cdd858d1d06387cf2a\"\n    },\n    \"name\": \"get_available_appointment_slots\",\n    \"args\": {\n        \"from_date\": \"2025-05-27\",\n        \"to_date\": \"2025-05-31\",\n        \"practitioner_id\": \"1691339493396719233\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/agent/function-call", "host": ["localhost"], "port": "4000", "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "2080"}, {"key": "ETag", "value": "W/\"820-pL8xIx4oMr0i6j4Cojt7Du0lxr8\""}, {"key": "Date", "value": "Mon, 26 May 2025 14:53:33 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"slots\": {\n            \"available_times\": [\n                {\n                    \"appointment_start\": \"2025-05-26T23:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-26T23:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T00:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T00:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T01:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T02:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T02:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T03:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T03:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T04:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T23:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-27T23:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T00:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T00:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T01:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T02:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T02:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T03:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T03:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T04:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T23:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-28T23:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T00:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T00:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T01:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T02:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T02:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T03:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T03:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T04:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T23:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-29T23:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T00:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T00:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T01:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T02:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T02:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T03:00:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T03:30:00Z\"\n                },\n                {\n                    \"appointment_start\": \"2025-05-30T04:00:00Z\"\n                }\n            ],\n            \"total_entries\": 40,\n            \"links\": {\n                \"self\": \"https://api.au4.cliniko.com/v1/businesses/1684108806834889941/practitioners/1691339493396719233/appointment_types/1684108806004417595/available_times?from=2025-05-27&to=2025-05-31&page=1\"\n            }\n        }\n    }\n}"}, {"name": "local get_patients", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_837085a4cdd858d1d06387cf2a\"\n    },\n    \"name\": \"get_patient_details\",\n    \"args\": {\n        \"patient_first_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"patient_last_name\": \"<PERSON><PERSON><PERSON>\",\n        \"patient_dob\": \"1998-07-01\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/agent/function-call", "host": ["localhost"], "port": "4000", "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "5927"}, {"key": "ETag", "value": "W/\"1727-Y4w9jBK4ylEqQcMx5WgG4GbNWgI\""}, {"key": "Date", "value": "Mon, 26 May 2025 14:55:26 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"patients\": [\n            {\n                \"accepted_email_marketing\": false,\n                \"accepted_privacy_policy\": null,\n                \"accepted_sms_marketing\": false,\n                \"address_1\": null,\n                \"address_2\": null,\n                \"address_3\": null,\n                \"appointment_notes\": null,\n                \"archived_at\": null,\n                \"city\": null,\n                \"country\": \"India\",\n                \"country_code\": \"IN\",\n                \"created_at\": \"2025-05-26T11:56:28Z\",\n                \"custom_fields\": null,\n                \"date_of_birth\": \"1998-07-01\",\n                \"deleted_at\": null,\n                \"dva_card_number\": null,\n                \"email\": null,\n                \"emergency_contact\": null,\n                \"first_name\": \"<PERSON><PERSON><PERSON>\",\n                \"gender\": null,\n                \"gender_identity\": null,\n                \"id\": \"1694161499007755580\",\n                \"invoice_default_to\": null,\n                \"invoice_email\": null,\n                \"invoice_extra_information\": null,\n                \"label\": \"<PERSON><PERSON><PERSON> Gautam\",\n                \"last_name\": \"<PERSON><PERSON><PERSON>\",\n                \"medicare\": null,\n                \"medicare_reference_number\": null,\n                \"merged_at\": null,\n                \"notes\": null,\n                \"occupation\": null,\n                \"old_reference_id\": null,\n                \"patient_phone_numbers\": [\n                    {\n                        \"normalized_number\": \"91**********\",\n                        \"number\": \"**********\",\n                        \"phone_type\": \"Mobile\"\n                    }\n                ],\n                \"post_code\": null,\n                \"preferred_first_name\": null,\n                \"pronouns\": null,\n                \"receives_cancellation_emails\": true,\n                \"receives_confirmation_emails\": true,\n                \"referral_source\": null,\n                \"reminder_type\": \"SMS & Email\",\n                \"sex\": null,\n                \"state\": null,\n                \"time_zone\": null,\n                \"title\": null,\n                \"updated_at\": \"2025-05-26T11:56:28Z\",\n                \"latest_booking\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/bookings/1694161499636901134\"\n                    }\n                },\n                \"appointments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1694161499007755580/appointments\"\n                    }\n                },\n                \"attendees\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/attendees?q%5B%5D=patient_id%3A%3D1694161499007755580\"\n                    }\n                },\n                \"invoices\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1694161499007755580/invoices\"\n                    }\n                },\n                \"patient_attachments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1694161499007755580/patient_attachments\"\n                    }\n                },\n                \"medical_alerts\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1694161499007755580/medical_alerts\"\n                    }\n                },\n                \"relationships\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/relationships?q%5B%5D=patient_id%3A%3D1694161499007755580\"\n                    }\n                },\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1694161499007755580\"\n                }\n            },\n            {\n                \"accepted_email_marketing\": false,\n                \"accepted_privacy_policy\": null,\n                \"accepted_sms_marketing\": false,\n                \"address_1\": null,\n                \"address_2\": null,\n                \"address_3\": null,\n                \"appointment_notes\": null,\n                \"archived_at\": null,\n                \"city\": null,\n                \"country\": \"India\",\n                \"country_code\": \"IN\",\n                \"created_at\": \"2025-05-23T04:29:54Z\",\n                \"custom_fields\": null,\n                \"date_of_birth\": \"1998-07-01\",\n                \"deleted_at\": null,\n                \"dva_card_number\": null,\n                \"email\": null,\n                \"emergency_contact\": null,\n                \"first_name\": \"Siddharth\",\n                \"gender\": null,\n                \"gender_identity\": null,\n                \"id\": \"1691762406872589580\",\n                \"invoice_default_to\": null,\n                \"invoice_email\": null,\n                \"invoice_extra_information\": null,\n                \"label\": \"Siddharth Gautam\",\n                \"last_name\": \"Gautam\",\n                \"medicare\": null,\n                \"medicare_reference_number\": null,\n                \"merged_at\": null,\n                \"notes\": null,\n                \"occupation\": null,\n                \"old_reference_id\": null,\n                \"patient_phone_numbers\": [\n                    {\n                        \"normalized_number\": \"91**********\",\n                        \"number\": \"**********\",\n                        \"phone_type\": \"Mobile\"\n                    }\n                ],\n                \"post_code\": null,\n                \"preferred_first_name\": null,\n                \"pronouns\": null,\n                \"receives_cancellation_emails\": true,\n                \"receives_confirmation_emails\": true,\n                \"referral_source\": null,\n                \"reminder_type\": \"SMS & Email\",\n                \"sex\": \"male\",\n                \"state\": null,\n                \"time_zone\": null,\n                \"title\": null,\n                \"updated_at\": \"2025-05-23T04:31:01Z\",\n                \"appointments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691762406872589580/appointments\"\n                    }\n                },\n                \"attendees\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/attendees?q%5B%5D=patient_id%3A%3D1691762406872589580\"\n                    }\n                },\n                \"invoices\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691762406872589580/invoices\"\n                    }\n                },\n                \"patient_attachments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691762406872589580/patient_attachments\"\n                    }\n                },\n                \"medical_alerts\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691762406872589580/medical_alerts\"\n                    }\n                },\n                \"relationships\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/relationships?q%5B%5D=patient_id%3A%3D1691762406872589580\"\n                    }\n                },\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1691762406872589580\"\n                }\n            },\n            {\n                \"accepted_email_marketing\": false,\n                \"accepted_privacy_policy\": null,\n                \"accepted_sms_marketing\": false,\n                \"address_1\": \"\",\n                \"address_2\": null,\n                \"address_3\": null,\n                \"appointment_notes\": null,\n                \"archived_at\": null,\n                \"city\": null,\n                \"country\": \"India\",\n                \"country_code\": \"IN\",\n                \"created_at\": \"2025-05-22T15:08:24Z\",\n                \"custom_fields\": null,\n                \"date_of_birth\": \"1998-07-01\",\n                \"deleted_at\": null,\n                \"dva_card_number\": null,\n                \"email\": null,\n                \"emergency_contact\": null,\n                \"first_name\": \"Siddharth\",\n                \"gender\": null,\n                \"gender_identity\": null,\n                \"id\": \"1691358994209187556\",\n                \"invoice_default_to\": null,\n                \"invoice_email\": null,\n                \"invoice_extra_information\": null,\n                \"label\": \"Siddharth Gautam\",\n                \"last_name\": \"Gautam\",\n                \"medicare\": null,\n                \"medicare_reference_number\": null,\n                \"merged_at\": null,\n                \"notes\": null,\n                \"occupation\": null,\n                \"old_reference_id\": null,\n                \"patient_phone_numbers\": [\n                    {\n                        \"normalized_number\": \"91**********\",\n                        \"number\": \"**********\",\n                        \"phone_type\": \"Mobile\"\n                    }\n                ],\n                \"post_code\": null,\n                \"preferred_first_name\": null,\n                \"pronouns\": null,\n                \"receives_cancellation_emails\": true,\n                \"receives_confirmation_emails\": true,\n                \"referral_source\": null,\n                \"reminder_type\": \"SMS & Email\",\n                \"sex\": \"male\",\n                \"state\": null,\n                \"time_zone\": null,\n                \"title\": null,\n                \"updated_at\": \"2025-05-23T04:32:41Z\",\n                \"latest_booking\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/bookings/1691763807686239774\"\n                    }\n                },\n                \"appointments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691358994209187556/appointments\"\n                    }\n                },\n                \"attendees\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/attendees?q%5B%5D=patient_id%3A%3D1691358994209187556\"\n                    }\n                },\n                \"invoices\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691358994209187556/invoices\"\n                    }\n                },\n                \"patient_attachments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691358994209187556/patient_attachments\"\n                    }\n                },\n                \"medical_alerts\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1691358994209187556/medical_alerts\"\n                    }\n                },\n                \"relationships\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/relationships?q%5B%5D=patient_id%3A%3D1691358994209187556\"\n                    }\n                },\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patients/1691358994209187556\"\n                }\n            }\n        ]\n    }\n}"}, {"name": "prod get_clinic_details", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_952adaf8c1ecb08e382362765b\"\n    },\n    \"name\": \"get_clinic_details\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/agent/function-call", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 27 May 2025 14:49:14 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"2ec-aZohcVBTMJzY7dHkilfFmrbE60I\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=T6KTF8XDAeHADiCa5tcmo5dgqFIrFcHcX6r0O%2BV2bGmFV211tBcQZetK%2BbGEc0OSpxP0V4PY6QohNcGeCJjiYQN90QNf%2FVM8W7Rgjd4hILwUyukvRA%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "9466515668573a1e-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"diagnostic_services\": [\n            {\n                \"name\": \"Neurological Consultations (Neurologists & Neurosurgeons)\",\n                \"is_referral_required\": true,\n                \"_id\": \"682f3008567d425290a985eb\"\n            },\n            {\n                \"name\": \"Diagnostic Tests\",\n                \"is_referral_required\": false,\n                \"_id\": \"682f3008567d425290a985ec\"\n            },\n            {\n                \"name\": \"Paediatric EEGs\",\n                \"is_referral_required\": false,\n                \"_id\": \"682f3008567d425290a985ed\"\n            },\n            {\n                \"name\": \"Occupational Therapy\",\n                \"is_referral_required\": false,\n                \"_id\": \"682f3008567d425290a985ee\"\n            },\n            {\n                \"name\": \"Physiotherapy\",\n                \"is_referral_required\": false,\n                \"_id\": \"682f3008567d425290a985ef\"\n            },\n            {\n                \"name\": \"Speech Pathology\",\n                \"is_referral_required\": false,\n                \"_id\": \"682f3008567d425290a985f0\"\n            }\n        ],\n        \"clinic_addresses\": [\n            \"Suite 3 & 4, 17 Napier Close, Deakin ACT 2600\",\n            \"Suite 3 & 4, 1 Grazier Lane, Belconnen ACT 2617\"\n        ]\n    }\n}"}, {"name": "local-get-patient-referral", "originalRequest": {"method": "POST", "header": [{"key": "x-retell-signature", "value": "v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"call\": {\n        \"agent_id\": \"agent_837085a4cdd858d1d06387cf2a\"\n    },\n    \"name\": \"get_patient_referral_source\",\n    \"args\": {\n        \"patient_id\": \"1695404883005417438\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/agent/function-call", "host": ["localhost"], "port": "4000", "path": ["api", "agent", "function-call"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "1216"}, {"key": "ETag", "value": "W/\"4c0-wxv3w4bgRqbA5acW1z9+xrj5QTw\""}, {"key": "Date", "value": "Fri, 30 May 2025 10:34:15 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"patient_referrals\": [\n            {\n                \"archived_at\": null,\n                \"attendee_ids\": [],\n                \"closed\": false,\n                \"closed_at\": null,\n                \"created_at\": \"2025-05-28T05:08:01Z\",\n                \"expiry_date\": null,\n                \"id\": \"1695405470962952178\",\n                \"include_cancelled_attendees\": null,\n                \"include_dna_attendees\": null,\n                \"issue_date\": \"2025-05-21\",\n                \"max_invoiceable_amount\": null,\n                \"max_sessions\": null,\n                \"name\": \"Appointment with <PERSON><PERSON>ologist\",\n                \"notes\": null,\n                \"referral\": true,\n                \"referral_type\": null,\n                \"updated_at\": \"2025-05-28T05:08:01Z\",\n                \"contact\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/contacts/1691347699737044351\"\n                    }\n                },\n                \"patient\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patients/1695404883005417438\"\n                    }\n                },\n                \"attendees\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patient_cases/1695405470962952178/attendees\"\n                    }\n                },\n                \"bookings\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patient_cases/1695405470962952178/bookings\"\n                    }\n                },\n                \"patient_attachments\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patient_cases/1695405470962952178/patient_attachments\"\n                    }\n                },\n                \"invoices\": {\n                    \"links\": {\n                        \"self\": \"https://api.au4.cliniko.com/v1/patient_cases/1695405470962952178/invoices\"\n                    }\n                },\n                \"links\": {\n                    \"self\": \"https://api.au4.cliniko.com/v1/patient_cases/1695405470962952178\"\n                },\n                \"referring_doctor_name\": \"Ronak Patel\"\n            }\n        ]\n    }\n}"}]}, {"name": "Admin - List all users", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}, {"key": "x-id-token", "value": "eyJraWQiOiJPTlVJZlBlQXVUTUp6SGN5WmM5RkYzMkE4R3JiU1VzSDdHN2VPUWoxSnVBPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EBtD1U9q5fV2OYedpKAbqRYNo022bxKhbmFZlTkzzzx-5GjHSs59SCJKJRvnD9ZxmaaQS-qyUx5oxOdCy9WOSXSXHfqzlo91o_R6vIF89tiRitDefqx6pOFwHQAOGJkBAPmWhyhbNrXj7ay-Xx0y_nPHVaSR86vMdyr26Ccczr3kgHx_pKvvJg_EpuVQk-VhK2ctNhHO4Npi7cEGbPjAiRovxiB672rdwYHM-I3JIPfHNWta4ZdhZpp3sqvkgxUZ4mt90rXOd7se0QuhkZ5LCPv9Mhm-trKaxC0hDkCRpmLlW4VHOwrbnk135-zWANzkto7coK-ZjngMKlE77-HglQ", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/users", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "users"]}}, "response": [{"name": "Admin - List all users", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}, {"key": "x-id-token", "value": "eyJraWQiOiJPTlVJZlBlQXVUTUp6SGN5WmM5RkYzMkE4R3JiU1VzSDdHN2VPUWoxSnVBPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EBtD1U9q5fV2OYedpKAbqRYNo022bxKhbmFZlTkzzzx-5GjHSs59SCJKJRvnD9ZxmaaQS-qyUx5oxOdCy9WOSXSXHfqzlo91o_R6vIF89tiRitDefqx6pOFwHQAOGJkBAPmWhyhbNrXj7ay-Xx0y_nPHVaSR86vMdyr26Ccczr3kgHx_pKvvJg_EpuVQk-VhK2ctNhHO4Npi7cEGbPjAiRovxiB672rdwYHM-I3JIPfHNWta4ZdhZpp3sqvkgxUZ4mt90rXOd7se0QuhkZ5LCPv9Mhm-trKaxC0hDkCRpmLlW4VHOwrbnk135-zWANzkto7coK-ZjngMKlE77-HglQ", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/users", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 05 Jun 2025 05:04:46 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"a0-kcAM/e90QJn2kTzmrFLpEt2F9LI\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=YAi9A8U5gzP53NgF0nE2NTUT%2BY9U3%2BmgUuHLMjxc4cmRkK%2BhBYua1ElC%2Bh4sS2XvATnLV34tT3ivWRmTzwSNe8DMwCzRtkdyMt2%2F8pUrNOa4PYXNAA%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "94ad2190cde380b0-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"_id\": \"6822e7db4b8b521b826a051f\",\n            \"email\": \"<EMAIL>\",\n            \"__v\": 0\n        },\n        {\n            \"_id\": \"682321584b8b521b826a0540\",\n            \"email\": \"<EMAIL>\",\n            \"__v\": 0\n        }\n    ]\n}"}]}, {"name": "Admin - List all clinics", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.ONY-Z46wQnul0W7RqlYay82aSDFM4vosfL1PiWkPtYY", "type": "text"}], "url": {"raw": "localhost:4000/api/admin/clinics", "host": ["localhost"], "port": "4000", "path": ["api", "admin", "clinics"]}}, "response": [{"name": "List all clinics", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}, {"key": "x-id-token", "value": "eyJraWQiOiJPTlVJZlBlQXVUTUp6SGN5WmM5RkYzMkE4R3JiU1VzSDdHN2VPUWoxSnVBPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EBtD1U9q5fV2OYedpKAbqRYNo022bxKhbmFZlTkzzzx-5GjHSs59SCJKJRvnD9ZxmaaQS-qyUx5oxOdCy9WOSXSXHfqzlo91o_R6vIF89tiRitDefqx6pOFwHQAOGJkBAPmWhyhbNrXj7ay-Xx0y_nPHVaSR86vMdyr26Ccczr3kgHx_pKvvJg_EpuVQk-VhK2ctNhHO4Npi7cEGbPjAiRovxiB672rdwYHM-I3JIPfHNWta4ZdhZpp3sqvkgxUZ4mt90rXOd7se0QuhkZ5LCPv9Mhm-trKaxC0hDkCRpmLlW4VHOwrbnk135-zWANzkto7coK-ZjngMKlE77-HglQ", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 05 Jun 2025 05:05:50 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"18c5-TcpwMvRsV9XIkIT64ooZBwZU5R0\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=s7OAud%2FnqTl8YVd428CSyBmItGF%2B8orlYkodXpCtJEiR7GX2P6lGLDPKdS4kU%2Bc6xWtTC77Qh15MgfgWJ3RH1xeH%2FD9mR%2B0AZYph4TrsHjMS2AKDhA%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "94ad231c5d7f80b0-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjYzNDM5MTQ0MDE1MTEyMTExLVNjcWNMRmJ6OU8yTnVuakp4UVNoWDV5M0JPMnVoZmhp-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1663438324993369597\",\n                    \"appointment_type_id\": \"1663438324280337701\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"diagnostic_services\": [],\n            \"_id\": \"682326494b8b521b826a0546\",\n            \"clinic_name\": \"ACT Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"\\u202a+************\\u202c\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T11:00:25.121Z\",\n            \"updated_at\": \"2025-05-13T11:00:25.121Z\",\n            \"__v\": 0\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjYzNDM5MTQ0MDE1MTEyMTExLVNjcWNMRmJ6OU8yTnVuakp4UVNoWDV5M0JPMnVoZmhp-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1663438324993369597\",\n                    \"appointment_type_id\": \"1663438324280337701\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"diagnostic_services\": [],\n            \"_id\": \"682328114b8b521b826a0556\",\n            \"clinic_name\": \"ACT Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"\\u202a+************\\u202c\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T11:08:01.330Z\",\n            \"updated_at\": \"2025-05-13T11:08:01.330Z\",\n            \"__v\": 0\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjYzNDM5MTQ0MDE1MTEyMTExLVNjcWNMRmJ6OU8yTnVuakp4UVNoWDV5M0JPMnVoZmhp-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1663438324993369597\",\n                    \"appointment_type_id\": \"1663438324280337701\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"diagnostic_services\": [],\n            \"_id\": \"682328534b8b521b826a055f\",\n            \"clinic_name\": \"ACT Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"\\u202a+919313635965\\u202c\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T11:09:07.653Z\",\n            \"updated_at\": \"2025-05-13T11:09:07.653Z\",\n            \"__v\": 0\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjYzNDM5MTQ0MDE1MTEyMTExLVNjcWNMRmJ6OU8yTnVuakp4UVNoWDV5M0JPMnVoZmhp-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1663438324993369597\",\n                    \"appointment_type_id\": \"1663438324280337701\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"diagnostic_services\": [],\n            \"_id\": \"68232aff4b8b521b826a0567\",\n            \"clinic_name\": \"ACT Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"+919313635965\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T11:20:31.291Z\",\n            \"updated_at\": \"2025-05-21T06:53:54.965Z\",\n            \"__v\": 0,\n            \"agent_id\": \"agent_b3c30067427c98658b6523323b\"\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjkwMzAyNzE5MjI4NjUwODM0LThYdnBRZkk3bXd6bTRFTjNWNGNUMXRpL0ZZa2k4REdu-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1663438324993369597\",\n                    \"appointment_type_id\": \"1663438324280337701\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"_id\": \"683338e076a33b05b5f6421b\",\n            \"clinic_name\": \"A.C.T Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology1.com.au/\",\n            \"human_transfer_destination_number\": \"+919313635965\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-25T15:36:00.391Z\",\n            \"updated_at\": \"2025-05-25T15:37:49.809Z\",\n            \"diagnostic_services\": [],\n            \"__v\": 0,\n            \"agent_id\": \"agent_aa1cdc658f2757b9b6d07f63b3\"\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjkzODYzMTI1NTk5NjU5Mzg0LWJiYzZUMXl0QzJzMWZjQ2llVUZPU1lNek1LZnlSaVpN-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1684108806834889941\",\n                    \"appointment_type_id\": \"1684108806004417595\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"clinic_addresses\": [],\n            \"_id\": \"6833cd6576a33b05b5f6423f\",\n            \"clinic_name\": \"A.C.T Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology01.com.au/\",\n            \"human_transfer_destination_number\": \"+919313635965\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-26T02:09:41.952Z\",\n            \"updated_at\": \"2025-05-26T02:10:29.780Z\",\n            \"diagnostic_services\": [],\n            \"__v\": 0,\n            \"agent_id\": \"agent_c8d5f832862eda163d4f324e5f\"\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1684108806834889941\",\n                    \"appointment_type_id\": \"1684108806004417595\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"_id\": \"6822e9604b8b521b826a0528\",\n            \"clinic_name\": \"A C T Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"+919313635965\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T06:40:32.204Z\",\n            \"updated_at\": \"2025-05-30T09:56:57.350Z\",\n            \"__v\": 0,\n            \"agent_id\": \"agent_952adaf8c1ecb08e382362765b\",\n            \"diagnostic_services\": [\n                {\n                    \"name\": \"Neurological Consultations (Neurologists & Neurosurgeons)\",\n                    \"is_referral_required\": true\n                },\n                {\n                    \"name\": \"Diagnostic Tests\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Paediatric EEGs\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Occupational Therapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Physiotherapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Speech Pathology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Rheumatologist\",\n                    \"is_referral_required\": true\n                }\n            ],\n            \"clinic_addresses\": [\n                {\n                    \"address\": \"Deakin Location\",\n                    \"full_address\": \"Suite 3 & 4, 17 Napier Close, Deakin A-C-T 2600\",\n                    \"business_location_id\": \"1684108806834889941\"\n                },\n                {\n                    \"address\": \"Belconnen Location\",\n                    \"full_address\": \"Suite 3 & 4, 1 Grazier Lane, Belconnen A-C-T 2617\",\n                    \"business_location_id\": \"1696253568828319476\"\n                }\n            ]\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjk2OTM4MzEwNTE1MDQxNzI2LXMxbjN4MDN0UUVRQS9kMXYrdk9vQys5ZUY2dzVrVU5o-au4\"\n                },\n                \"custom_fields\": {\n                    \"appointment_type_id\": \"1696873667406865565\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"_id\": \"6822e8a34b8b521b826a0521\",\n            \"clinic_name\": \"Better Health Family Clinic\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://www.betterhealthfamilyclinic.com.au/\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T06:37:23.894Z\",\n            \"updated_at\": \"2025-05-30T10:15:46.659Z\",\n            \"__v\": 0,\n            \"human_transfer_destination_number\": \"+************\",\n            \"diagnostic_services\": [\n                {\n                    \"name\": \"GP\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Pathology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Radiology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Dietitian\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Physiotherapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Psychology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Respiratory Physician\",\n                    \"is_referral_required\": true\n                }\n            ],\n            \"clinic_addresses\": [\n                {\n                    \"address\": \"Hampton Park\",\n                    \"full_address\": \"127- 129 Somerville Road Hampton Park, VIC 3976\",\n                    \"business_location_id\": \"1696873668027623186\"\n                }\n            ],\n            \"agent_id\": \"agent_bb066591863e86c8cc81434df1\"\n        }\n    ]\n}"}]}, {"name": "Admin get twilio-phone-numbers", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}, {"key": "x-id-token", "value": "eyJraWQiOiJPTlVJZlBlQXVUTUp6SGN5WmM5RkYzMkE4R3JiU1VzSDdHN2VPUWoxSnVBPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EBtD1U9q5fV2OYedpKAbqRYNo022bxKhbmFZlTkzzzx-5GjHSs59SCJKJRvnD9ZxmaaQS-qyUx5oxOdCy9WOSXSXHfqzlo91o_R6vIF89tiRitDefqx6pOFwHQAOGJkBAPmWhyhbNrXj7ay-Xx0y_nPHVaSR86vMdyr26Ccczr3kgHx_pKvvJg_EpuVQk-VhK2ctNhHO4Npi7cEGbPjAiRovxiB672rdwYHM-I3JIPfHNWta4ZdhZpp3sqvkgxUZ4mt90rXOd7se0QuhkZ5LCPv9Mhm-trKaxC0hDkCRpmLlW4VHOwrbnk135-zWANzkto7coK-ZjngMKlE77-HglQ", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/twilio-phone-numbers", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "twilio-phone-numbers"]}}, "response": [{"name": "Admin get twilio-phone-numbers", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}, {"key": "x-id-token", "value": "eyJraWQiOiJPTlVJZlBlQXVUTUp6SGN5WmM5RkYzMkE4R3JiU1VzSDdHN2VPUWoxSnVBPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EBtD1U9q5fV2OYedpKAbqRYNo022bxKhbmFZlTkzzzx-5GjHSs59SCJKJRvnD9ZxmaaQS-qyUx5oxOdCy9WOSXSXHfqzlo91o_R6vIF89tiRitDefqx6pOFwHQAOGJkBAPmWhyhbNrXj7ay-Xx0y_nPHVaSR86vMdyr26Ccczr3kgHx_pKvvJg_EpuVQk-VhK2ctNhHO4Npi7cEGbPjAiRovxiB672rdwYHM-I3JIPfHNWta4ZdhZpp3sqvkgxUZ4mt90rXOd7se0QuhkZ5LCPv9Mhm-trKaxC0hDkCRpmLlW4VHOwrbnk135-zWANzkto7coK-ZjngMKlE77-HglQ", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/twilio-phone-numbers", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "twilio-phone-numbers"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 05 Jun 2025 05:06:48 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"9ec-0E16iilaoLfJ6GHdi76aE6WIeW8\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=1oC50tBP9UWL0Q2oA%2Fi6t%2FCug1Q0CrqthjsOFLDrmExEd0WDuHH7PIAROnSuXpRf%2BfZvn2P0wFPEDCOOE15mizvPBlu9uRVJH83T3qk05%2F9EKbIsGg%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "94ad2484afcc80b0-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"accountSid\": \"ACfeeb819a34402815609b95d71ac20a26\",\n            \"addressSid\": null,\n            \"addressRequirements\": \"none\",\n            \"apiVersion\": \"2010-04-01\",\n            \"beta\": false,\n            \"capabilities\": {\n                \"fax\": false,\n                \"mms\": true,\n                \"sms\": true,\n                \"voice\": true\n            },\n            \"dateCreated\": \"2025-03-17T07:04:15.000Z\",\n            \"dateUpdated\": \"2025-04-10T13:45:29.000Z\",\n            \"friendlyName\": \"(*************\",\n            \"identitySid\": null,\n            \"phoneNumber\": \"+***********\",\n            \"origin\": \"twilio\",\n            \"sid\": \"PN80a47864cbd87514cce857cf063a65d3\",\n            \"smsApplicationSid\": \"\",\n            \"smsFallbackMethod\": \"POST\",\n            \"smsFallbackUrl\": \"\",\n            \"smsMethod\": \"POST\",\n            \"smsUrl\": \"https://dev-phone-1093-6349.twil.io/incoming-message-handler\",\n            \"statusCallback\": \"https://dev-phone-1093-6349.twil.io/sync-call-history\",\n            \"statusCallbackMethod\": \"POST\",\n            \"trunkSid\": null,\n            \"uri\": \"/2010-04-01/Accounts/ACfeeb819a34402815609b95d71ac20a26/IncomingPhoneNumbers/PN80a47864cbd87514cce857cf063a65d3.json\",\n            \"voiceReceiveMode\": \"voice\",\n            \"voiceApplicationSid\": \"\",\n            \"voiceCallerIdLookup\": false,\n            \"voiceFallbackMethod\": \"POST\",\n            \"voiceFallbackUrl\": \"https://handler.twilio.com/twiml/EHc3d18150c0158ed583b7e6a6a8e9164b\",\n            \"voiceMethod\": \"POST\",\n            \"voiceUrl\": \"https://dev-phone-1093-6349.twil.io/incoming-call-handler\",\n            \"emergencyStatus\": \"Active\",\n            \"emergencyAddressSid\": null,\n            \"emergencyAddressStatus\": \"unregistered\",\n            \"bundleSid\": null,\n            \"status\": \"in-use\"\n        },\n        {\n            \"accountSid\": \"ACfeeb819a34402815609b95d71ac20a26\",\n            \"addressSid\": \"ADf1a48bc53ebc82c6c442d3465ea31ddf\",\n            \"addressRequirements\": \"local\",\n            \"apiVersion\": \"2010-04-01\",\n            \"beta\": false,\n            \"capabilities\": {\n                \"fax\": false,\n                \"mms\": false,\n                \"sms\": false,\n                \"voice\": true\n            },\n            \"dateCreated\": \"2025-03-18T23:10:41.000Z\",\n            \"dateUpdated\": \"2025-04-26T07:55:58.000Z\",\n            \"friendlyName\": \"***********\",\n            \"identitySid\": null,\n            \"phoneNumber\": \"+***********\",\n            \"origin\": \"twilio\",\n            \"sid\": \"PN1fc1b98f9b6dbe860dd5651921f687d4\",\n            \"smsApplicationSid\": \"\",\n            \"smsFallbackMethod\": \"POST\",\n            \"smsFallbackUrl\": \"\",\n            \"smsMethod\": \"POST\",\n            \"smsUrl\": \"https://dev-phone-6050-7698.twil.io/incoming-message-handler\",\n            \"statusCallback\": \"https://dev-phone-6050-7698.twil.io/sync-call-history\",\n            \"statusCallbackMethod\": \"POST\",\n            \"trunkSid\": \"TK890266d71bee3f40860e1d8cdec4b27e\",\n            \"uri\": \"/2010-04-01/Accounts/ACfeeb819a34402815609b95d71ac20a26/IncomingPhoneNumbers/PN1fc1b98f9b6dbe860dd5651921f687d4.json\",\n            \"voiceReceiveMode\": \"voice\",\n            \"voiceApplicationSid\": null,\n            \"voiceCallerIdLookup\": false,\n            \"voiceFallbackMethod\": \"POST\",\n            \"voiceFallbackUrl\": \"https://api.synthflow.ai/v2/calls\",\n            \"voiceMethod\": \"POST\",\n            \"voiceUrl\": \"https://dev-phone-6050-7698.twil.io/incoming-call-handler\",\n            \"emergencyStatus\": \"Active\",\n            \"emergencyAddressSid\": null,\n            \"emergencyAddressStatus\": \"unregistered\",\n            \"bundleSid\": null,\n            \"status\": \"in-use\"\n        }\n    ]\n}"}]}, {"name": "admin/register-admin", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.ONY-Z46wQnul0W7RqlYay82aSDFM4vosfL1PiWkPtYY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/api/admin/register-admin", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "admin", "register-admin"]}}, "response": [{"name": "admin/register-admin", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.ONY-Z46wQnul0W7RqlYay82aSDFM4vosfL1PiWkPtYY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/api/admin/register-admin", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "admin", "register-admin"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "80"}, {"key": "ETag", "value": "W/\"50-cg4gZ1CZByw/uFzXMj8rGnp2BBw\""}, {"key": "Date", "value": "Mon, 12 May 2025 11:03:03 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"_id\": \"6821d5674b2632b0e5f95b48\",\n        \"__v\": 0\n    }\n}"}]}, {"name": "Admin list all admins", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "x-id-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/admins", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "admins"]}}, "response": [{"name": "Admin list all admins", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "x-id-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/admin/admins", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "admins"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 05 Jun 2025 06:57:58 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"10d-KSoCdlVZQNT3msVGzWJPmEz88oE\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=ETY%2BM5NE8oYeIMMQUBr%2BwxaOwAOKmYQrPRo5bnfyCMMh6cxd%2FeyueSbgj0X%2F2zKRrf11l7nW4hAv66qX6hTzHSowdhBdrv0dGPBwGVWlwFDqI55RlQ%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "94adc763aeb840cf-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"_id\": \"68413f72a4713233507c6808\",\n            \"first_name\": \"<PERSON>arun\",\n            \"last_name\": \"<PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"uid\": \"892e1408-e0d1-7062-4bbc-d5be0b6e1d5b\",\n            \"created_at\": \"2025-06-05T06:55:46.936Z\",\n            \"updated_at\": \"2025-06-05T06:55:46.936Z\",\n            \"__v\": 0,\n            \"admin_level\": \"owner\"\n        }\n    ]\n}"}]}, {"name": "List user clinics", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJraWQiOiJobXdPc3ZVOXc2R29HQkY1R2VFXC9Vam14XC9HVDRoMmxtNlFnRys5YzhVUlE9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T-RefHZcfnxcJkswKajdDHaSb1VVqoosg9s6Dr8qA0XyX13ZpqA43F5Dgiee0aSNHTpq9cmvevUz822b7YvyRLOgzt5bXJOH7nhKfXn9hYFS65YWJMGN--ZX3mZXhW5Q5TtVMnqFUU2RB5e2SXz1ongBsFzCoPa3ElCwyWBzmQivgE1nUSPIzgIIgSNWIYq3U0JIsm9CjUgf-0BBt82Xlvw0bVD0slLyCP0noZku8gDEO3gBPRgszlQsookTQ15-6hN6TtbdgBe5ecr5n2lUJUpppS4zx_RKYp2tTScK06NqR3Q1UgAzcXDaq5fp_UBbvNFwhH24CBB26qj3iQ_knA", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "response": [{"name": "List user clinics", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "url": {"raw": "localhost:4000/api/db/clinics", "host": ["localhost"], "port": "4000", "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "234"}, {"key": "ETag", "value": "W/\"ea-4eWTvLxtFQDNhsgEXHxaNLrSCIk\""}, {"key": "Date", "value": "Mon, 12 May 2025 13:59:50 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"_id\": \"6821fde1a57e294a49fcbc4d\",\n            \"clinic_name\": \"ACT Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-12T13:55:45.981Z\",\n            \"updated_at\": \"2025-05-12T13:55:45.981Z\",\n            \"__v\": 0\n        }\n    ]\n}"}, {"name": "Prod", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.S6opNg8HXDxt-kdUhNYLCNSft4PzGEF1rO4C8Cf9fsY", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 30 May 2025 10:08:07 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"9b1-lcDMrDKOzNfgNritAZQM/xx0C2M\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=XsleSgpOaJcDpr8wtQp%2BRgqjEHQ116DWDSeGaAaczD4ALqjFry6COlY0eV4E39nhnXtxR6%2BOk%2BJ7EnzYz1u3UpNarg5PscW5KSFZui2NqkzPSYk%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "947d6dabbadf2e53-B<PERSON>"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjk2OTM4MzEwNTE1MDQxNzI2LXMxbjN4MDN0UUVRQS9kMXYrdk9vQys5ZUY2dzVrVU5o-au4\"\n                },\n                \"custom_fields\": {\n                    \"appointment_type_id\": \"1696873667406865565\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"_id\": \"6822e8a34b8b521b826a0521\",\n            \"clinic_name\": \"Better Health Family Clinic\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://www.betterhealthfamilyclinic.com.au/\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T06:37:23.894Z\",\n            \"updated_at\": \"2025-05-30T10:06:33.865Z\",\n            \"__v\": 0,\n            \"human_transfer_destination_number\": \"+************\",\n            \"diagnostic_services\": [\n                {\n                    \"name\": \"GP\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Pathology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Radiology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Dietitian\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Physiotherapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Psychology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Respiratory Physician\",\n                    \"is_referral_required\": true\n                }\n            ],\n            \"clinic_addresses\": [\n                {\n                    \"address\": \"Hampton Park\",\n                    \"full_address\": \"127- 129 Somerville Road Hampton Park, VIC 3976\",\n                    \"business_location_id\": \"1696873668027623186\"\n                }\n            ]\n        },\n        {\n            \"crm_details\": {\n                \"auth_details\": {\n                    \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n                },\n                \"custom_fields\": {\n                    \"business_id\": \"1684108806834889941\",\n                    \"appointment_type_id\": \"1684108806004417595\"\n                },\n                \"name\": \"CLINIKO\"\n            },\n            \"_id\": \"6822e9604b8b521b826a0528\",\n            \"clinic_name\": \"A C T Neurology Centre\",\n            \"clinic_email\": \"<EMAIL>\",\n            \"clinic_website\": \"https://actneurology.com.au/\",\n            \"human_transfer_destination_number\": \"+919313635965\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-13T06:40:32.204Z\",\n            \"updated_at\": \"2025-05-30T09:56:57.350Z\",\n            \"__v\": 0,\n            \"agent_id\": \"agent_952adaf8c1ecb08e382362765b\",\n            \"diagnostic_services\": [\n                {\n                    \"name\": \"Neurological Consultations (Neurologists & Neurosurgeons)\",\n                    \"is_referral_required\": true\n                },\n                {\n                    \"name\": \"Diagnostic Tests\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Paediatric EEGs\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Occupational Therapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Physiotherapy\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Speech Pathology\",\n                    \"is_referral_required\": false\n                },\n                {\n                    \"name\": \"Rheumatologist\",\n                    \"is_referral_required\": true\n                }\n            ],\n            \"clinic_addresses\": [\n                {\n                    \"address\": \"Deakin Location\",\n                    \"full_address\": \"Suite 3 & 4, 17 Napier Close, Deakin A-C-T 2600\",\n                    \"business_location_id\": \"1684108806834889941\"\n                },\n                {\n                    \"address\": \"Belconnen Location\",\n                    \"full_address\": \"Suite 3 & 4, 1 Grazier Lane, Belconnen A-C-T 2617\",\n                    \"business_location_id\": \"1696253568828319476\"\n                }\n            ]\n        }\n    ]\n}"}]}, {"name": "Create User Clinic", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"clinic_name\": \"ACT Neurology Centre\",\n    \"clinic_email\": \"<EMAIL>\",\n    \"clinic_website\": \"https://actneurology.com.au/\",\n    \"crm_details\": {\n      \"name\": \"CLINIKO\",\n      \"auth_details\": {\n        \"api_key\":\n          \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n      },\n      \"custom_fields\": {\n        \"business_id\": \"1684108806834889941\",\n        \"appointment_type_id\": \"1684108806004417595\"\n      }\n    },\n    \"human_transfer_destination_number\": \"+************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "response": [{"name": "Create User Clinic", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.HL9DPleihD9QuCDpe22q3Cahi5Wm2SVTMkDrFRfMekE", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"clinic_name\": \"ACT Neurology Centre\",\n    \"clinic_email\": \"<EMAIL>\",\n    \"clinic_website\": \"https://actneurology.com.au/\",\n    \"crm_details\": {\n      \"name\": \"CLINIKO\",\n      \"auth_details\": {\n        \"api_key\":\n          \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n      },\n      \"custom_fields\": {\n        \"business_id\": \"1684108806834889941\",\n        \"appointment_type_id\": \"1684108806004417595\"\n      }\n    },\n    \"human_transfer_destination_number\": \"+************\",\n    \"diagnostic_services\":[\n    {\n      \"name\":\"Neurological Consultations (Neurologists & Neurosurgeons)\",\n      \"is_referral_required\":true\n    },\n    {\n      \"name\":\"Diagnostic Tests\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Paediatric EEGs\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Occupational Therapy\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Physiotherapy\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Speech Pathology\",\n      \"is_referral_required\":false\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/db/clinics", "host": ["localhost"], "port": "4000", "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "232"}, {"key": "ETag", "value": "W/\"e8-k6R2DABcPkRDNRLbEYPKYd0ZXII\""}, {"key": "Date", "value": "Mon, 12 May 2025 13:55:46 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"is_active\": true,\n        \"_id\": \"6821fde1a57e294a49fcbc4d\",\n        \"created_at\": \"2025-05-12T13:55:45.981Z\",\n        \"updated_at\": \"2025-05-12T13:55:45.981Z\",\n        \"__v\": 0\n    }\n}"}, {"name": "Prod", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"clinic_name\": \"ACT Neurology Centre\",\n    \"clinic_email\": \"<EMAIL>\",\n    \"clinic_website\": \"https://actneurology.com.au/\",\n    \"crm_details\": {\n      \"name\": \"CLINIKO\",\n      \"auth_details\": {\n        \"api_key\":\n          \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n      },\n      \"custom_fields\": {\n        \"business_id\": \"1684108806834889941\",\n        \"appointment_type_id\": \"1684108806004417595\"\n      }\n    },\n    \"human_transfer_destination_number\": \"+************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 06:40:32 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"239-ukJKbKNSKsMfFzhdsXcDekocTyA\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=h2a7hRoQ%2B14LB6t1nvSQ4ugFz9IwwSDdYIP9wF64JDamuZYGWIbZXnniePLRg%2FWUN1KBnyzKnFcT%2Bk4lkD2jT3BxJWEwZPU6PDFHjtLLAzFazESWSCg61CRyfSUWZ%2BOzv4D7u94v4XU%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f02a36aa4c3cac-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"clinic_website\": \"https://actneurology.com.au/\",\n        \"crm_details\": {\n            \"name\": \"CLINIKO\",\n            \"auth_details\": {\n                \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n            },\n            \"custom_fields\": {\n                \"business_id\": \"1684108806834889941\",\n                \"appointment_type_id\": \"1684108806004417595\"\n            }\n        },\n        \"human_transfer_destination_number\": \"+************\",\n        \"is_active\": true,\n        \"_id\": \"6822e9604b8b521b826a0528\",\n        \"created_at\": \"2025-05-13T06:40:32.204Z\",\n        \"updated_at\": \"2025-05-13T06:40:32.204Z\",\n        \"__v\": 0\n    }\n}"}]}, {"name": "Get user clinic details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.3kgSB3gHowxLu0TiGwZDUPgolNNIazCIQUL60y1_FCc", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/db/clinics/6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics", "6822e9604b8b521b826a0528"]}}, "response": [{"name": "Get user clinic details", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "url": {"raw": "localhost:4000/api/db/clinics/6821fde1a57e294a49fcbc4d", "host": ["localhost"], "port": "4000", "path": ["api", "db", "clinics", "6821fde1a57e294a49fcbc4d"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "232"}, {"key": "ETag", "value": "W/\"e8-PxUvq1eibpzsrZBmPjMfRkyvKEc\""}, {"key": "Date", "value": "Mon, 12 May 2025 14:25:11 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"_id\": \"6821fde1a57e294a49fcbc4d\",\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"is_active\": true,\n        \"created_at\": \"2025-05-12T13:55:45.981Z\",\n        \"updated_at\": \"2025-05-12T13:55:45.981Z\",\n        \"__v\": 0\n    }\n}"}, {"name": "Prod", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/db/clinics/6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics", "6822e9604b8b521b826a0528"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 06:48:32 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"267-76vjMRED5yTe3vgfGlfdVYGS7s8\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=hNbxT47aUfc2Yu3a8JdRG0KxHUNDGkMxovWU60jpIpv9zLBrHtRPC%2BIRZjGKLsf6Hfy79uQApnKivbUK6UzvBhaZvPv%2BjCXp72ZZ1%2B%2FWnKAEp1pNAbEl3dUiIzWE2SGzQEw3Dj6eS9g%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f035f1dee63e40-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"crm_details\": {\n            \"auth_details\": {\n                \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n            },\n            \"custom_fields\": {\n                \"business_id\": \"1684108806834889941\",\n                \"appointment_type_id\": \"1684108806004417595\"\n            },\n            \"name\": \"CLINIKO\"\n        },\n        \"_id\": \"6822e9604b8b521b826a0528\",\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"clinic_website\": \"https://actneurology.com.au/\",\n        \"human_transfer_destination_number\": \"+************\",\n        \"is_active\": true,\n        \"created_at\": \"2025-05-13T06:40:32.204Z\",\n        \"updated_at\": \"2025-05-13T06:43:57.168Z\",\n        \"__v\": 0,\n        \"agent_id\": \"agent_aad9ed15cc0825a8113bf2a247\"\n    }\n}"}]}, {"name": "Update User Clinic", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.3kgSB3gHowxLu0TiGwZDUPgolNNIazCIQUL60y1_FCc", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"_id\":\"6822e8a34b8b521b826a0521\",\n  \"diagnostic_services\":[\n    {\n      \"name\":\"Neurological Consultations (Neurologists & Neurosurgeons)\",\n      \"is_referral_required\":true\n    },\n    {\n      \"name\":\"Diagnostic Tests\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Paediatric EEGs\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Occupational Therapy\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Physiotherapy\",\n      \"is_referral_required\":false\n    },\n    {\n      \"name\":\"Speech Pathology\",\n      \"is_referral_required\":false\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "response": [{"name": "Update User Clinic", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"6821fde1a57e294a49fcbc4d\",\n    \"clinic_website\": \"https://actneurology.com.au/\",\n    \"crm_details\": {\n      \"name\": \"CLINIKO\",\n      \"auth_details\": {\n        \"api_key\":\n          \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n      },\n      \"custom_fields\": {\n        \"business_id\": \"1684108806834889941\",\n        \"appointment_type_id\": \"1684108806004417595\"\n      }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/db/clinics", "host": ["localhost"], "port": "4000", "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "517"}, {"key": "ETag", "value": "W/\"205-A26DTSQFxP+tygMXIE+NLCBoPUw\""}, {"key": "Date", "value": "Mon, 12 May 2025 15:17:23 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"crm_details\": {\n            \"auth_details\": {\n                \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n            },\n            \"custom_fields\": {\n                \"business_id\": \"1684108806834889941\",\n                \"appointment_type_id\": \"1684108806004417595\"\n            },\n            \"name\": \"CLINIKO\"\n        },\n        \"_id\": \"6821fde1a57e294a49fcbc4d\",\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"is_active\": true,\n        \"created_at\": \"2025-05-12T13:55:45.981Z\",\n        \"updated_at\": \"2025-05-12T15:17:23.404Z\",\n        \"__v\": 0,\n        \"clinic_website\": \"https://actneurology.com.au/\"\n    }\n}"}, {"name": "Prod", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.S6opNg8HXDxt-kdUhNYLCNSft4PzGEF1rO4C8Cf9fsY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"_id\": \"6822e9604b8b521b826a0528\",\n    \"clinic_addresses\": [\n        {\n            \"address\": \"Deakin Location\",\n            \"full_address\": \"Suite 3 & 4, 17 Napier Close, Deakin A-C-T 2600\",\n            \"business_location_id\": \"1684108806834889941\"\n        },\n        {\n            \"address\": \"Belconnen Location\",\n            \"full_address\": \"Suite 3 & 4, 1 Grazier Lane, Belconnen A-C-T 2617\",\n            \"business_location_id\": \"1696253568828319476\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/db/clinics", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "db", "clinics"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 07:14:00 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"239-egp+IEY3uiHPVHxAx6WeU0dqe8E\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=z9S12KeLxmArvo20nvvytdCGRb172kz9DKAaEI23cE%2FDYu4cIA8GEURVwkMBK69svtkKq8KiZQOIEWAqAv4L9Lk6oWhy%2B%2BNiLFg%2B6av5t1DLBrFIyUHH%2F6hDLP247dv1BWzTdrnpBik%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f05b3b5ccf3e3f-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"crm_details\": {\n            \"auth_details\": {\n                \"api_key\": \"MS0xNjg0MTEwOTc1MjU2NTAzNTM0LW5kSzdKYVJXdkhwWmxmUGNiVE93YTZhMlEvL2pZeDMz-au4\"\n            },\n            \"custom_fields\": {\n                \"business_id\": \"1684108806834889941\",\n                \"appointment_type_id\": \"1684108806004417595\"\n            },\n            \"name\": \"CLINIKO\"\n        },\n        \"_id\": \"6822e8a34b8b521b826a0521\",\n        \"clinic_name\": \"ACT Neurology Centre\",\n        \"clinic_email\": \"<EMAIL>\",\n        \"clinic_website\": \"https://actneurology.com.au/\",\n        \"is_active\": true,\n        \"created_at\": \"2025-05-13T06:37:23.894Z\",\n        \"updated_at\": \"2025-05-13T07:13:59.998Z\",\n        \"__v\": 0,\n        \"human_transfer_destination_number\": \"+************\"\n    }\n}"}]}, {"name": "Create voice agent", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/voice-agent?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "voice-agent"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "response": [{"name": "Create voice agent", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6InZhcnVuQGdyb3dlYXN5LmFpIiwiaWQiOiI2ODJmMmNlN2JhZWZjMDQ3NTI4MmM5OGQiLCJyb2xlIjoidXNlciIsImlhdCI6MTc0NzkyMjM4MywiZXhwIjoxNzQ4NTI3MTgzfQ.miQXzNVA0rrZl9lCAftwOSsiMb_XznpVmJ57WsVWQfw", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"pronunciation_dict\": [{\n        \"word\": \"A.C.T Neurology\",\n        \"alphabet\": \"ipa\",\n        \"phoneme\": \"eɪ.siː.tiː njʊəˈrɒləʤi\"\n    }]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/api/retell/voice-agent?clinic_id=682f2d46baefc0475282c992", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "retell", "voice-agent"], "query": [{"key": "clinic_id", "value": "682f2d46baefc0475282c992"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "591"}, {"key": "ETag", "value": "W/\"24f-T0bUdfPW/6PbyazSxrvNoPu8q0Y\""}, {"key": "Date", "value": "Mon, 12 May 2025 16:06:17 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"agent_id\": \"agent_c8e7c250a770bbc4b188238760\",\n        \"channel\": \"voice\",\n        \"last_modification_timestamp\": 1747065977072,\n        \"agent_name\": \"ACT Neurology Centre-1747065976772\",\n        \"response_engine\": {\n            \"llm_id\": \"llm_aaae67859daebb4592abe9b0bc9f\",\n            \"type\": \"retell-llm\",\n            \"version\": 0\n        },\n        \"language\": \"en-US\",\n        \"opt_out_sensitive_data_storage\": false,\n        \"opt_in_signed_url\": false,\n        \"version\": 0,\n        \"is_published\": false,\n        \"post_call_analysis_model\": \"gpt-4o-mini\",\n        \"voice_id\": \"custom_voice_465d2a62fb4bf25abc3aa3dc1f\",\n        \"max_call_duration_ms\": 3600000,\n        \"voicemail_detection_timeout_ms\": 30000,\n        \"allow_user_dtmf\": true,\n        \"user_dtmf_options\": {}\n    }\n}"}, {"name": "ACT prod", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6InZhcnVuQGdyb3dlYXN5LmFpIiwiaWQiOiI2ODIyZTdkYjRiOGI1MjFiODI2YTA1MWYiLCJyb2xlIjoidXNlciIsImlhdCI6MTc0Nzk3MzkyOCwiZXhwIjoxNzQ4NTc4NzI4fQ.X0YviKjfx65PkSBsCxE0J2RDAuxX0gUl8Vvq3yLq8x0", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"pronunciation_dict\": [{\n        \"word\": \"A.C.T Neurology\",\n        \"alphabet\": \"ipa\",\n        \"phoneme\": \"eɪ.siː.tiː njʊəˈrɒləʤi\"\n    }]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/retell/voice-agent?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "voice-agent"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 06:43:57 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"24f-1jgiEZB6jB5n+BzbtTEyhEkfXuk\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=vfsW7RwStSymC3EaMjwLejpCA9L0pUewUaN92RA2acA3Mah4oW6W36xxm4YtxcWk2E%2FTWtAR4XB%2BXFHc0JUBACGV%2FLMn2JdZzxBckf7IE1UWyFcOTHf4YClRpE5ilfRSgY4u8sdIsaE%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f02f33ad7c3a87-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"agent_id\": \"agent_aad9ed15cc0825a8113bf2a247\",\n        \"channel\": \"voice\",\n        \"last_modification_timestamp\": 1747118637085,\n        \"agent_name\": \"ACT Neurology Centre-1747118636991\",\n        \"response_engine\": {\n            \"llm_id\": \"llm_5e1c1e8a5562c1a1255daa4c1fd5\",\n            \"type\": \"retell-llm\",\n            \"version\": 0\n        },\n        \"language\": \"en-US\",\n        \"opt_out_sensitive_data_storage\": false,\n        \"opt_in_signed_url\": false,\n        \"version\": 0,\n        \"is_published\": false,\n        \"post_call_analysis_model\": \"gpt-4o-mini\",\n        \"voice_id\": \"custom_voice_465d2a62fb4bf25abc3aa3dc1f\",\n        \"max_call_duration_ms\": 3600000,\n        \"voicemail_detection_timeout_ms\": 30000,\n        \"allow_user_dtmf\": true,\n        \"user_dtmf_options\": {}\n    }\n}"}, {"name": "betterhealth", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.S6opNg8HXDxt-kdUhNYLCNSft4PzGEF1rO4C8Cf9fsY", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/voice-agent?clinic_id=6822e8a34b8b521b826a0521", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "voice-agent"], "query": [{"key": "clinic_id", "value": "6822e8a34b8b521b826a0521"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 30 May 2025 10:15:46 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"27d-pDrvkFsfJUbe75sn2FpKJM8wOCU\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=OvIy0Ux4ZvkocYQjg8GZZNYC1aI9j4EC8p0cr11uSJD%2BfhNxthFwU20nKNU0ZE%2BHDf7TuaQLwZ1llk6WQ92NUwcfyxP6F7Ds1dqXS0PF9yf4ift5SQ%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "947d78dd1dac47e3-B<PERSON>"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"agent_id\": \"agent_bb066591863e86c8cc81434df1\",\n        \"channel\": \"voice\",\n        \"last_modification_timestamp\": 1748600146578,\n        \"agent_name\": \"<EMAIL>-1748600146487\",\n        \"response_engine\": {\n            \"llm_id\": \"llm_7199c4f583c87b3cd01cf7a5a9a2\",\n            \"type\": \"retell-llm\",\n            \"version\": 0\n        },\n        \"language\": \"en-US\",\n        \"opt_out_sensitive_data_storage\": false,\n        \"opt_in_signed_url\": false,\n        \"version\": 0,\n        \"is_published\": false,\n        \"post_call_analysis_model\": \"gpt-4o-mini\",\n        \"voice_id\": \"custom_voice_c2095e295634dc56c2b8ebe3be\",\n        \"max_call_duration_ms\": 3600000,\n        \"pronunciation_dictionary\": [],\n        \"voicemail_detection_timeout_ms\": 30000,\n        \"allow_user_dtmf\": true,\n        \"user_dtmf_options\": {}\n    }\n}"}]}, {"name": "Get clinic Voice agent", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/agent?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "agent"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "response": [{"name": "Get clinic Voice agent", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "url": {"raw": "http://localhost:4000/api/retell/agent?clinic_id=6821fde1a57e294a49fcbc4d", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "retell", "agent"], "query": [{"key": "clinic_id", "value": "6821fde1a57e294a49fcbc4d"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "591"}, {"key": "ETag", "value": "W/\"24f-T0bUdfPW/6PbyazSxrvNoPu8q0Y\""}, {"key": "Date", "value": "Mon, 12 May 2025 16:10:07 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"agent_id\": \"agent_c8e7c250a770bbc4b188238760\",\n        \"channel\": \"voice\",\n        \"last_modification_timestamp\": 1747065977072,\n        \"agent_name\": \"ACT Neurology Centre-1747065976772\",\n        \"response_engine\": {\n            \"llm_id\": \"llm_aaae67859daebb4592abe9b0bc9f\",\n            \"type\": \"retell-llm\",\n            \"version\": 0\n        },\n        \"language\": \"en-US\",\n        \"opt_out_sensitive_data_storage\": false,\n        \"opt_in_signed_url\": false,\n        \"version\": 0,\n        \"is_published\": false,\n        \"post_call_analysis_model\": \"gpt-4o-mini\",\n        \"voice_id\": \"custom_voice_465d2a62fb4bf25abc3aa3dc1f\",\n        \"max_call_duration_ms\": 3600000,\n        \"voicemail_detection_timeout_ms\": 30000,\n        \"allow_user_dtmf\": true,\n        \"user_dtmf_options\": {}\n    }\n}"}, {"name": "Prod", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/agent?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "agent"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 10:49:36 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"24f-1jgiEZB6jB5n+BzbtTEyhEkfXuk\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=GOr89dbDO%2FM50BmWgpsjtzT%2Bt58md%2FPN9z28ZE49Ez4kQbzTI4yzxEE2XrFoJ6NIJkFm7K8TEdcQHyTfAq1eUqaTIvzXnVsR3wpLZ7THXmRobJGVKq0kMisb9MZ%2FO9ZxFsOxe2akqZk%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f1970d3cb8ff73-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"agent_id\": \"agent_aad9ed15cc0825a8113bf2a247\",\n        \"channel\": \"voice\",\n        \"last_modification_timestamp\": 1747118637085,\n        \"agent_name\": \"ACT Neurology Centre-1747118636991\",\n        \"response_engine\": {\n            \"llm_id\": \"llm_5e1c1e8a5562c1a1255daa4c1fd5\",\n            \"type\": \"retell-llm\",\n            \"version\": 0\n        },\n        \"language\": \"en-US\",\n        \"opt_out_sensitive_data_storage\": false,\n        \"opt_in_signed_url\": false,\n        \"version\": 0,\n        \"is_published\": false,\n        \"post_call_analysis_model\": \"gpt-4o-mini\",\n        \"voice_id\": \"custom_voice_465d2a62fb4bf25abc3aa3dc1f\",\n        \"max_call_duration_ms\": 3600000,\n        \"voicemail_detection_timeout_ms\": 30000,\n        \"allow_user_dtmf\": true,\n        \"user_dtmf_options\": {}\n    }\n}"}]}, {"name": "GET knowledge bases", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "url": {"raw": "http://localhost:4000/api/retell/knowledge-bases?clinic_id=6821fde1a57e294a49fcbc4d", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6821fde1a57e294a49fcbc4d"}]}}, "response": [{"name": "GET knowledge bases", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "url": {"raw": "http://localhost:4000/api/retell/knowledge-bases?clinic_id=6821fde1a57e294a49fcbc4d", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6821fde1a57e294a49fcbc4d"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "503"}, {"key": "ETag", "value": "W/\"1f7-k52bekWeixF2g5j3rG34+lxNcDU\""}, {"key": "Date", "value": "Mon, 12 May 2025 16:26:36 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"knowledge_base_id\": \"knowledge_base_5bbe2862d86b3065\",\n            \"knowledge_base_name\": \"ACT Neurology Centre-1747067140152\",\n            \"knowledge_base_sources\": [\n                {\n                    \"source_id\": \"kb_source_6a0dd7eb02e33fbd\",\n                    \"content_url\": \"https://dxc03zgurdly9.cloudfront.net/knowledge_base_5bbe2862d86b3065/bec6e05c.txt\",\n                    \"type\": \"text\",\n                    \"title\": \"FAQs\"\n                },\n                {\n                    \"source_id\": \"kb_source_2ef7a46d354e8fb3\",\n                    \"type\": \"url\",\n                    \"url\": \"https://actneurology.com.au/\"\n                }\n            ],\n            \"enable_auto_refresh\": false,\n            \"status\": \"complete\",\n            \"user_modified_timestamp\": 1747067141024\n        }\n    ]\n}"}]}, {"name": "Create agent knowledge base", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.S6opNg8HXDxt-kdUhNYLCNSft4PzGEF1rO4C8Cf9fsY", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "/Users/<USER>/Downloads/better-health-kb.txt"}]}, "url": {"raw": "https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e8a34b8b521b826a0521", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6822e8a34b8b521b826a0521"}]}}, "response": [{"name": "Create agent knowledge base", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.a1T-XiH8leep6IyQQPPqea_mrdH1ggq1t_8uDvEqNME", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"urls\": [\n        \"https://actneurology.com.au/\"\n    ],\n    \"texts\": [\n        {\n            \"title\": \"FAQs\",\n            \"text\": \"Q: What are your hours?\\nA: We’re open Monday to Friday, 8:30 AM to 5:00 PM. Saturdays by appointment.\\n\\nQ: Where are you located?\\nA: We’re located at Suite 3 & 4, 17 Napier Close, Deakin ACT 2600, and Suite 18, 40 Mary Potter Circuit, Bruce, ACT 2617.\\n\\nQ: Who are the doctors there?\\nA: We have <PERSON>, <PERSON>, Dr <PERSON>, <PERSON>, <PERSON>, and many other excellent specialists.\\n\\nQ: Do you offer telehealth?\\nA: Yes, we do offer telehealth for certain types of appointments. I can connect you with our team to check if your consultation is eligible.\\n\\nQ: What’s the cost?\\nA: Costs can differ based on the service and your referral—some services are eligible for Medicare rebates. I can connect you with our team for more details.\\n\\nQ: Do you bulk bill?\\nA: Some services may be Medicare-supported. We can confirm based on your referral. I can connect you with our team for more details.\\n\\nQ: Do I need a referral?\\nA: Yes, for specialist consultations, a referral is required.\\n\\nQ: Is there parking?\\nA: Yes, we have free and accessible parking available on-site and are also situated near public transport stations.\\n\\nQ: How do I get there by public transport?\\nA: ACTION bus routes 3 and 934 stop nearby. They run between Woden and Belconnen via the city.\\n\\nQ: Is there a taxi service?\\nA: There’s a taxi rank at the hospital and a public phone available to call a taxi.\\n\\nQ: Do you offer community transport?\\nA: You might be eligible. You can learn more at transport.act.gov.au.\\n\\nQ: Can I get travel assistance?\\nA: If you live in a rural area, visit iptaas.health.nsw.gov.au for help with travel and accommodation.\\n\\nQ: Do you do EEGs?\\nA: Yes, we perform diagnostic EEGs and paediatric EEGs onsite.\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/api/retell/knowledge-bases?clinic_id=6821fde1a57e294a49fcbc4d", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6821fde1a57e294a49fcbc4d"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "214"}, {"key": "ETag", "value": "W/\"d6-rvDmqDzihXHDu08bHtQeKollPx4\""}, {"key": "Date", "value": "Mon, 12 May 2025 16:25:42 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"knowledge_base_id\": \"knowledge_base_5bbe2862d86b3065\",\n        \"knowledge_base_name\": \"ACT Neurology Centre-1747067140152\",\n        \"enable_auto_refresh\": false,\n        \"status\": \"in_progress\",\n        \"user_modified_timestamp\": 1747067141024\n    }\n}"}, {"name": "Prod", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"urls\": [\n        \"https://actneurology.com.au/\"\n    ],\n    \"texts\": [\n        {\n            \"title\": \"FAQs\",\n            \"text\": \"Q: What are your hours?\\nA: We’re open Monday to Friday, 8:30 AM to 5:00 PM. Saturdays by appointment.\\n\\nQ: Where are you located?\\nA: We’re located at Suite 3 & 4, 17 Napier Close, Deakin ACT 2600, and Suite 18, 40 Mary Potter Circuit, Bruce, ACT 2617.\\n\\nQ: Who are the doctors there?\\nA: We have <PERSON>, <PERSON>, Dr <PERSON>, <PERSON>, <PERSON>, and many other excellent specialists.\\n\\nQ: Do you offer telehealth?\\nA: Yes, we do offer telehealth for certain types of appointments. I can connect you with our team to check if your consultation is eligible.\\n\\nQ: What’s the cost?\\nA: Costs can differ based on the service and your referral—some services are eligible for Medicare rebates. I can connect you with our team for more details.\\n\\nQ: Do you bulk bill?\\nA: Some services may be Medicare-supported. We can confirm based on your referral. I can connect you with our team for more details.\\n\\nQ: Do I need a referral?\\nA: Yes, for specialist consultations, a referral is required.\\n\\nQ: Is there parking?\\nA: Yes, we have free and accessible parking available on-site and are also situated near public transport stations.\\n\\nQ: How do I get there by public transport?\\nA: ACTION bus routes 3 and 934 stop nearby. They run between Woden and Belconnen via the city.\\n\\nQ: Is there a taxi service?\\nA: There’s a taxi rank at the hospital and a public phone available to call a taxi.\\n\\nQ: Do you offer community transport?\\nA: You might be eligible. You can learn more at transport.act.gov.au.\\n\\nQ: Can I get travel assistance?\\nA: If you live in a rural area, visit iptaas.health.nsw.gov.au for help with travel and accommodation.\\n\\nQ: Do you do EEGs?\\nA: Yes, we perform diagnostic EEGs and paediatric EEGs onsite.\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 06:46:20 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"d6-EHJtk8i7Q+mZVpKcSaDpEdHDrdo\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=9XDyqn2pdw4aCYQ89GtISsYJRBoCAinX1Brxz46zV%2BYYS6qq4BUnBwvI0Pa2C%2Few5q39MZWA%2FYmdGDuFc4ZTJMbFn3HaOo2gCjJKv%2F0QYvjR8KtBxfu%2F5rlu5PL9T%2B94BAP7eJ%2BB6N8%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f032aec920425a-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"knowledge_base_id\": \"knowledge_base_21dafb5209b6b23c\",\n        \"knowledge_base_name\": \"ACT Neurology Centre-1747118779094\",\n        \"enable_auto_refresh\": false,\n        \"status\": \"in_progress\",\n        \"user_modified_timestamp\": 1747118779476\n    }\n}"}, {"name": "Using file prod", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "/Users/<USER>/Downloads/act-kb.txt"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 15 May 2025 04:35:26 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"dc-wUkImysRco086Cp0n1mr1S23ITc\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=qeYz9Oi8xjds%2B3T2JV8%2F3McnIVULgPCQZB2GmgJDbFpPsgLCvsLtDdX%2F%2FqTAXLtvzctgxRceBZNZgnJ6LJKAX%2Bep1jERR7PZ%2FtxMmYhvp%2FdHNZ%2BmpmrubpH3wQHyN53qKw47mbONITA%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93ffedb25f4e4457-B<PERSON>"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"knowledge_base_id\": \"knowledge_base_830e787be0fc847a\",\n        \"knowledge_base_name\": \"<EMAIL>-1747283725329\",\n        \"enable_auto_refresh\": false,\n        \"status\": \"in_progress\",\n        \"user_modified_timestamp\": 1747283725742\n    }\n}"}, {"name": "better-health", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.S6opNg8HXDxt-kdUhNYLCNSft4PzGEF1rO4C8Cf9fsY", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "/Users/<USER>/Downloads/better-health-kb.txt"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e8a34b8b521b826a0521", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "knowledge-bases"], "query": [{"key": "clinic_id", "value": "6822e8a34b8b521b826a0521"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 30 May 2025 10:18:21 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"dc-TO7+9Wg02sPVH9Y5NW2MzluYtEI\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=jwzm5zhN%2BnyzYGVcX55rG6GODZIOdpKPGzgyIW3ZyoVSLDSoerNAZq7PbacMShE%2BhjHuOBtaiXh6fLwAozH%2FupmhjlW4VpFPwb65BPP6PMnQ8juaCw%3D%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "947d7ca3cde63aab-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"knowledge_base_id\": \"knowledge_base_645952d1c83da875\",\n        \"knowledge_base_name\": \"info@betterhealthfamilycli-1748600300545\",\n        \"enable_auto_refresh\": false,\n        \"status\": \"in_progress\",\n        \"user_modified_timestamp\": 1748600300924\n    }\n}"}]}, {"name": "Admin get call analytics", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.l2ECUvm13SiU0qrojc0TGaGv4LmWDS7rXrtbpGhh4MY", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/analytics/list-calls?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "analytics", "list-calls"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "response": []}, {"name": "User clinic call analytics", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.erjZ_hpqOTNnTwFvb6pE6QGlg5SC8_QYPXOtEelWBFs", "type": "text"}], "url": {"raw": "https://app.smartreception.ai/api/retell/analytics/list-calls?clinic_id=6822e9604b8b521b826a0528", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "retell", "analytics", "list-calls"], "query": [{"key": "clinic_id", "value": "6822e9604b8b521b826a0528"}]}}, "response": []}, {"name": "admin assign-twilio-phone-number", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.l2ECUvm13SiU0qrojc0TGaGv4LmWDS7rXrtbpGhh4MY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phone_number\": \"+***********\",\n    \"clinic_id\": \"6822e9604b8b521b826a0528\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/admin/retell/assign-twilio-number", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "retell", "assign-twilio-number"]}}, "response": []}, {"name": "Update phone number to agent", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.l2ECUvm13SiU0qrojc0TGaGv4LmWDS7rXrtbpGhh4MY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phone_number\": \"+***********\",\n    \"clinic_id\": \"6822e9604b8b521b826a0528\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/admin/retell/update-phone-number", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "retell", "update-phone-number"]}}, "response": [{"name": "Update phone number to agent", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.l2ECUvm13SiU0qrojc0TGaGv4LmWDS7rXrtbpGhh4MY", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"phone_number\": \"+***********\",\n    \"clinic_id\": \"6822e9604b8b521b826a0528\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://app.smartreception.ai/api/admin/retell/update-phone-number", "protocol": "https", "host": ["app", "smartreception", "ai"], "path": ["api", "admin", "retell", "update-phone-number"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 13 May 2025 16:06:48 GMT"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Etag", "value": "W/\"125-Get5sBnanHyhdGmbubiAZyMp4Yg\""}, {"key": "Cf-Cache-Status", "value": "DYNAMIC"}, {"key": "<PERSON><PERSON>", "value": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}"}, {"key": "Report-To", "value": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=AYooenLxho6ISKsi5rRdXusiQjVeazG%2FWFMTYgpNLjrdpX8JUilh2BHkpezXh1r%2F7D0ShAj8nZ4BSjVW7der%2Bcc0%2FKpIsttGVgh%2F2Y8lAneN4dd4BXVNezLrGtkcS85KJBk2Qru5F3I%3D\"}]}"}, {"key": "Content-Encoding", "value": "br"}, {"key": "Server", "value": "cloudflare"}, {"key": "CF-RAY", "value": "93f367b43d7c55a5-BOM"}, {"key": "alt-svc", "value": "h3=\":443\"; ma=86400"}], "cookie": [], "body": "{\n    \"data\": {\n        \"phone_number\": \"+***********\",\n        \"phone_number_type\": \"custom\",\n        \"phone_number_pretty\": \"+***********\",\n        \"nickname\": \"twilio-retell-cred-1\",\n        \"inbound_agent_id\": \"agent_90c01f9cf2b7c7e2351c0818a2\",\n        \"outbound_agent_id\": \"agent_90c01f9cf2b7c7e2351c0818a2\",\n        \"last_modification_timestamp\": 1745655468849\n    }\n}"}]}, {"name": "Signup User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "x-id-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_data\": {\n        \"first_name\": \"<PERSON>aru<PERSON>\",\n        \"last_name\": \"<PERSON>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/users", "host": ["localhost"], "port": "4000", "path": ["api", "users"]}}, "response": [{"name": "Signup User", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "x-id-token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_data\": {\n        \"first_name\": \"<PERSON>aru<PERSON>\",\n        \"last_name\": \"<PERSON>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:4000/api/users", "host": ["localhost"], "port": "4000", "path": ["api", "users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Vary", "value": "Origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "245"}, {"key": "ETag", "value": "W/\"f5-4WvZFSyGOBzT0erOjf9jEF90QIA\""}, {"key": "Date", "value": "Thu, 05 Jun 2025 05:58:48 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"data\": {\n        \"first_name\": \"Varun\",\n        \"last_name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"uid\": \"892e1408-e0d1-7062-4bbc-d5be0b6e1d5b\",\n        \"_id\": \"68413218a51c60b050bef81c\",\n        \"created_at\": \"2025-06-05T05:58:47.300Z\",\n        \"updated_at\": \"2025-06-05T05:58:47.300Z\",\n        \"__v\": 0\n    }\n}"}]}]}