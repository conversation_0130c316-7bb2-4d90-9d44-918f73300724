{"taskDefinitionArn": "arn:aws:ecs:ap-southeast-2:341801066689:task-definition/smart-reception-fe:1", "containerDefinitions": [{"name": "smart-reception-fe", "image": "http://341801066689.dkr.ecr.ap-southeast-2.amazonaws.com/smart-reception-fe:latest", "cpu": 0, "portMappings": [{"name": "smart-reception-fe-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "PORT", "value": "3000"}, {"name": "NODE_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/smart-reception-fe", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "ap-southeast-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "smart-reception-fe", "taskRoleArn": "arn:aws:iam::341801066689:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::341801066689:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2025-04-19T14:12:51.635Z", "registeredBy": "arn:aws:iam::341801066689:user/varun", "tags": []}